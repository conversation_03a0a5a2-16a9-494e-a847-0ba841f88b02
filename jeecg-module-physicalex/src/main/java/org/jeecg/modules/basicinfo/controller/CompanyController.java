package org.jeecg.modules.basicinfo.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.druid.util.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.common.system.vo.SelectTreeModel;
import org.jeecg.modules.basicinfo.entity.Company;
import org.jeecg.modules.basicinfo.service.ICompanyService;
import org.jeecg.modules.basicinfo.dto.CompanyAutoCompleteDTO;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.cache.annotation.Cacheable;

/**
 * @Description: 单位
 * @Author: jeecg-boot
 * @Date: 2024-04-09
 * @Version: V1.0
 */
@Api(tags = "单位")
@RestController
@RequestMapping("/basicinfo/company")
@Slf4j
public class CompanyController extends JeecgController<Company, ICompanyService> {
    @Autowired
    private ICompanyService companyService;

    /**
     * 根据id获取单位，id可能是childId，如果是childId，则返回其所属的根单位
     */
    @GetMapping("/getRootCompanyById")
    public Result<Company> getRootCompanyById(@RequestParam(name = "id", required = true) String id) {
        Company company = companyService.getRootCompanyById(id);
        if (company == null) {
            return Result.error("没有找到对应的单位信息！");
        }
        return Result.OK(company);
    }

    /**
     * 分页列表查询（标准接口）
     *
     * @param company
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "单位-分页列表查询")
    @ApiOperation(value = "单位-分页列表查询", notes = "单位-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<Company>> queryList(Company company,
                                           @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                           @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                           HttpServletRequest req) {
        QueryWrapper<Company> queryWrapper = QueryGenerator.initQueryWrapper(company, req.getParameterMap());
        queryWrapper.eq("del_flag", 0);
        Page<Company> page = new Page<Company>(pageNo, pageSize);
        IPage<Company> pageList = companyService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 分页列表查询
     *
     * @param company
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "单位-分页列表查询")
    @ApiOperation(value = "单位-分页列表查询", notes = "单位-分页列表查询")
    @GetMapping(value = "/rootList")
    public Result<IPage<Company>> queryPageList(Company company, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        String hasQuery = req.getParameter("hasQuery");
        if ("true".equals(hasQuery)) {
            QueryWrapper<Company> queryWrapper = QueryGenerator.initQueryWrapper(company, req.getParameterMap());
            queryWrapper.eq("del_flag", "0");
            List<Company> list = companyService.queryTreeListNoPage(queryWrapper);
            IPage<Company> pageList = new Page<>(1, 10, list.size());
            pageList.setRecords(list);
            return Result.OK(pageList);
        } else {
            String parentId = company.getPid();
            if (oConvertUtils.isEmpty(parentId)) {
                parentId = "0";
            }
            company.setPid(null);
            QueryWrapper<Company> queryWrapper = QueryGenerator.initQueryWrapper(company, req.getParameterMap());
            // 使用 eq 防止模糊查询
            queryWrapper.eq("pid", parentId);
            Page<Company> page = new Page<Company>(pageNo, pageSize);
            IPage<Company> pageList = companyService.page(page, queryWrapper);
            return Result.OK(pageList);
        }
    }

    /**
     * 【vue3专用】加载节点的子数据
     *
     * @param pid
     * @return
     */
    @RequestMapping(value = "/loadTreeChildren", method = RequestMethod.GET)
    public Result<List<SelectTreeModel>> loadTreeChildren(@RequestParam(name = "pid") String pid) {
        Result<List<SelectTreeModel>> result = new Result<>();
        try {
            List<SelectTreeModel> ls = companyService.queryListByPid(pid);
            result.setResult(ls);
            result.setSuccess(true);
        } catch (Exception e) {
            e.printStackTrace();
            result.setMessage(e.getMessage());
            result.setSuccess(false);
        }
        return result;
    }

    /**
     * 【vue3专用】加载一级节点/如果是同步 则所有数据
     *
     * @param async
     * @param pcode
     * @return
     */
    @RequestMapping(value = "/loadTreeRoot", method = RequestMethod.GET)
    public Result<List<SelectTreeModel>> loadTreeRoot(@RequestParam(name = "async") Boolean async, @RequestParam(name = "pcode") String pcode) {
        Result<List<SelectTreeModel>> result = new Result<>();
        try {
            List<SelectTreeModel> ls = companyService.queryListByCode(pcode);
            if (!async) {
                loadAllChildren(ls);
            }
            result.setResult(ls);
            result.setSuccess(true);
        } catch (Exception e) {
            e.printStackTrace();
            result.setMessage(e.getMessage());
            result.setSuccess(false);
        }
        return result;
    }

    /**
     * 【vue3专用】递归求子节点 同步加载用到
     *
     * @param ls
     */
    private void loadAllChildren(List<SelectTreeModel> ls) {
        for (SelectTreeModel tsm : ls) {
            List<SelectTreeModel> temp = companyService.queryListByPid(tsm.getKey());
            if (temp != null && temp.size() > 0) {
                tsm.setChildren(temp);
                loadAllChildren(temp);
            }
        }
    }

    /**
     * 获取子数据
     *
     * @param company
     * @param req
     * @return
     */
    //@AutoLog(value = "单位-获取子数据")
    @ApiOperation(value = "单位-获取子数据", notes = "单位-获取子数据")
    @GetMapping(value = "/childList")
    public Result<IPage<Company>> queryPageList(Company company, HttpServletRequest req) {
        QueryWrapper<Company> queryWrapper = QueryGenerator.initQueryWrapper(company, req.getParameterMap());
        List<Company> list = companyService.list(queryWrapper);
        IPage<Company> pageList = new Page<>(1, 10, list.size());
        pageList.setRecords(list);
        return Result.OK(pageList);
    }

    /**
     * 批量查询子节点
     *
     * @param parentIds 父ID（多个采用半角逗号分割）
     * @param parentIds
     * @return 返回 IPage
     * @return
     */
    //@AutoLog(value = "单位-批量获取子数据")
    @ApiOperation(value = "单位-批量获取子数据", notes = "单位-批量获取子数据")
    @GetMapping("/getChildListBatch")
    public Result getChildListBatch(@RequestParam("parentIds") String parentIds) {
        try {
            QueryWrapper<Company> queryWrapper = new QueryWrapper<>();
            List<String> parentIdList = Arrays.asList(parentIds.split(","));
            queryWrapper.in("pid", parentIdList);
            List<Company> list = companyService.list(queryWrapper);
            IPage<Company> pageList = new Page<>(1, 10, list.size());
            pageList.setRecords(list);
            return Result.OK(pageList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error("批量查询子节点失败：" + e.getMessage());
        }
    }

    /**
     * 添加
     *
     * @param company
     * @return
     */
    @AutoLog(value = "单位-添加")
    @ApiOperation(value = "单位-添加", notes = "单位-添加")
    @RequiresPermissions("basicinfo:company:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody Company company) {
        companyService.addCompany(company);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param company
     * @return
     */
    @AutoLog(value = "单位-编辑")
    @ApiOperation(value = "单位-编辑", notes = "单位-编辑")
    @RequiresPermissions("basicinfo:company:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody Company company) {
        companyService.updateCompany(company);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "单位-通过id删除")
    @ApiOperation(value = "单位-通过id删除", notes = "单位-通过id删除")
    @RequiresPermissions("basicinfo:company:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        companyService.deleteCompany(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "单位-批量删除")
    @ApiOperation(value = "单位-批量删除", notes = "单位-批量删除")
    @RequiresPermissions("basicinfo:company:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.companyService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功！");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "单位-通过id查询")
    @ApiOperation(value = "单位-通过id查询", notes = "单位-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<Company> queryById(@RequestParam(name = "id", required = true) String id) {
        Company company = companyService.getById(id);
        if (company == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(company);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param company
     */
    @RequiresPermissions("basicinfo:company:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Company company) {
        return super.exportXls(request, company, Company.class, "单位");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("basicinfo:company:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, Company.class);
    }

    /**
     * 搜索单位部门数据
     *
     * @param keyWord 搜索关键字
     * @return
     */
    @ApiOperation(value = "搜索单位部门数据", notes = "根据关键字搜索单位部门数据")
    @GetMapping(value = "/searchCompany")
    public Result<List<SelectTreeModel>> searchCompany(@RequestParam(name = "keyWord") String keyWord) {
        Result<List<SelectTreeModel>> result = new Result<>();
        try {
            // 构建查询条件
            QueryWrapper<Company> queryWrapper = new QueryWrapper<>();
            queryWrapper.like("name", keyWord).or().like("short_name", keyWord).or().like("org_code", keyWord);
            queryWrapper.eq("del_flag", 0);
            queryWrapper.orderByAsc("pid", "name");

            List<Company> list = companyService.list(queryWrapper);
            List<SelectTreeModel> treeList = new ArrayList<>();

            for (Company company : list) {
                SelectTreeModel treeModel = new SelectTreeModel();
                treeModel.setKey(company.getId());
                treeModel.setTitle(company.getName());
                treeModel.setValue(company.getId());
                treeModel.setParentId(company.getPid());
                boolean isLeaf = !StringUtils.equals(company.getHasChild(), "1");
                treeModel.setLeaf(isLeaf);
                treeList.add(treeModel);
            }

            result.setResult(treeList);
            result.setSuccess(true);
        } catch (Exception e) {
            e.printStackTrace();
            result.setMessage(e.getMessage());
            result.setSuccess(false);
        }
        return result;
    }

    /**
     * 自动完成接口 - 只查询父级单位（没有父ID的记录）
     *
     * @param keyword 搜索关键词（可选）
     * @param pageSize 返回数量限制，默认50
     * @return
     */
    @ApiOperation(value = "单位自动完成", notes = "根据关键词搜索父级单位，支持按名称、简称、助记码、编码搜索")
    @GetMapping(value = "/autoComplete")
    @Cacheable(key = "'company_auto_' + (#keyword != null ? #keyword : 'all') + '_' + #pageSize", unless = "#result == null || #result.result == null || #result.result.size() == 0")
    public Result<List<CompanyAutoCompleteDTO>> autoComplete(
            @RequestParam(name = "keyword", required = false) String keyword,
            @RequestParam(name = "pageSize", defaultValue = "50") Integer pageSize) {

        try {
            // 构建查询条件 - 只查询父级记录
            QueryWrapper<Company> queryWrapper = new QueryWrapper<>();

            // 只查询父级记录（pid为空或为"0"）
            queryWrapper.and(wrapper -> wrapper.isNull("pid").or().eq("pid", "").or().eq("pid", "0"));

            // 只查询未删除的记录
            queryWrapper.eq("del_flag", 0);

            // 如果有关键词，添加搜索条件
            if (oConvertUtils.isNotEmpty(keyword)) {
                String searchKeyword = keyword.trim();
                queryWrapper.and(wrapper ->
                    wrapper.like("name", searchKeyword)
                           .or().like("short_name", searchKeyword)
                           .or().like("help_char", searchKeyword)
                           .or().like("org_code", searchKeyword)
                );
            }

            // 排序：优先显示有助记码的，然后按名称排序
            queryWrapper.orderByDesc("help_char IS NOT NULL AND help_char != ''")
                       .orderByAsc("name");

            // 限制返回数量
            queryWrapper.last("LIMIT " + Math.min(pageSize, 100)); // 最多返回100条

            List<Company> companyList = companyService.list(queryWrapper);

            // 转换为DTO
            List<CompanyAutoCompleteDTO> dtoList = companyList.stream()
                    .map(company -> new CompanyAutoCompleteDTO(
                            company.getId(),
                            company.getName(),
                            company.getShortName(),
                            company.getHelpChar(),
                            company.getOrgCode(),
                            company.getTelephone(),
                            company.getAddress()
                    ))
                    .collect(Collectors.toList());

            return Result.OK(dtoList);

        } catch (Exception e) {
            log.error("获取单位自动完成数据失败", e);
            return Result.error("获取单位数据失败：" + e.getMessage());
        }
    }

}
