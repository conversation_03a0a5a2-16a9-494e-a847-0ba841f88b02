package org.jeecg.modules.basicinfo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 单位自动完成DTO
 * @Author: System
 * @Date: 2024-08-27
 * @Version: V1.0
 */
@Data
@ApiModel(value = "CompanyAutoCompleteDTO", description = "单位自动完成数据传输对象")
public class CompanyAutoCompleteDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 单位ID
     */
    @ApiModelProperty(value = "单位ID")
    private String id;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String name;

    /**
     * 单位简称
     */
    @ApiModelProperty(value = "单位简称")
    private String shortName;

    /**
     * 助记码
     */
    @ApiModelProperty(value = "助记码")
    private String helpChar;

    /**
     * 单位编码
     */
    @ApiModelProperty(value = "单位编码")
    private String orgCode;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String telephone;

    /**
     * 地址
     */
    @ApiModelProperty(value = "地址")
    private String address;

    /**
     * 构造函数
     */
    public CompanyAutoCompleteDTO() {
    }

    /**
     * 从Company实体构造DTO
     */
    public CompanyAutoCompleteDTO(String id, String name, String shortName, String helpChar, String orgCode, String telephone, String address) {
        this.id = id;
        this.name = name;
        this.shortName = shortName;
        this.helpChar = helpChar;
        this.orgCode = orgCode;
        this.telephone = telephone;
        this.address = address;
    }
}
