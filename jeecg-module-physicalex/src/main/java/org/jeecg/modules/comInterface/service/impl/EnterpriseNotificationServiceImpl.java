package org.jeecg.modules.comInterface.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.basicinfo.service.ISysSettingService;
import org.jeecg.modules.comInterface.service.IEnterpriseNotificationService;
import org.jeecg.modules.comInterface.vo.BatchResultVO;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * 企业端通知服务实现
 */
@Slf4j
@Service
public class EnterpriseNotificationServiceImpl implements IEnterpriseNotificationService {

    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private ISysSettingService sysSettingService;

    // 企业端URL配置键
    private static final String ENTERPRISE_URL_CONFIG_KEY = "ENTERPRISE_NOTIFICATION_URL";

    @Override
    public void notifyBatchProgress(String companyRegId, BatchResultVO<CustomerReg> result, String taskId) {

        try {
            result.setRegId(companyRegId);
            result.setTaskId(taskId);

            //1、从参数配置表中取到体检企业端url
            String enterpriseUrl = sysSettingService.getValueByCode(ENTERPRISE_URL_CONFIG_KEY);
            if (enterpriseUrl == null || enterpriseUrl.trim().isEmpty()) {
                log.warn("Enterprise notification URL not configured for key: {}", ENTERPRISE_URL_CONFIG_KEY);
                return;
            }

            //2、直接使用BatchResultVO作为通知数据，保持结构一致
            // 添加时间戳字段
            Map<String, Object> notificationData = new HashMap<>();
            notificationData.put("taskId", taskId);
            notificationData.put("regId", result.getRegId());
            notificationData.put("total", result.getTotal());
            notificationData.put("successCount", result.getSuccessCount());
            notificationData.put("failureCount", result.getFailureCount());
            notificationData.put("successList", result.getSuccessList());
            notificationData.put("failureList", result.getFailureList());
            notificationData.put("message", result.getMessage());
            notificationData.put("extra", result.getExtra());
            //notificationData.put("isAsync", result.getIsAsync());
            //notificationData.put("timestamp", System.currentTimeMillis());

            //3、设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.add("User-Agent", "PhysicalEx-Notification-Service");

            //4、创建请求实体
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(notificationData, headers);

            //5、发送通知
            log.info("Sending batch progress notification to enterprise: {}, companyRegId: {}, taskId: {}",
                    enterpriseUrl, companyRegId, taskId);

            ResponseEntity<String> response = restTemplate.exchange(
                    enterpriseUrl,
                HttpMethod.POST,
                requestEntity,
                String.class
            );

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("Successfully notified enterprise about batch progress for companyRegId: {}, taskId: {}",
                        companyRegId, taskId);
            } else {
                log.warn("Enterprise notification returned non-success status: {} for companyRegId: {}, taskId: {}",
                        response.getStatusCode(), companyRegId, taskId);
            }

        } catch (Exception e) {
            log.error("Failed to notify enterprise about batch progress for companyRegId: {}, taskId: {}",
                    companyRegId, taskId, e);
        }
    }


}
