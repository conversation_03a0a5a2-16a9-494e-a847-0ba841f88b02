# Company自动完成缓存配置
# 在主配置文件中通过 spring.profiles.include 引入此配置

spring:
  cache:
    # 缓存类型
    type: redis
    # 缓存名称配置
    cache-names:
      - companyAutoComplete
    redis:
      # 默认缓存过期时间（6小时）
      time-to-live: 21600000
      # 是否缓存空值
      cache-null-values: false
      # 键前缀
      key-prefix: "company_autocomplete_cache:"
      # 是否使用键前缀
      use-key-prefix: true

# 日志配置
logging:
  level:
    # Company模块缓存相关日志
    org.jeecg.modules.basicinfo.service.impl.CompanyServiceImpl: DEBUG
    org.jeecg.modules.basicinfo.controller.CompanyController: DEBUG
    # Spring缓存日志
    org.springframework.cache: DEBUG
