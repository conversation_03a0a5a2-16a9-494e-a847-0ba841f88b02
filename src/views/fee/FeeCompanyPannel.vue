<template>
  <div style="padding: 5px">
    <a-card size="small">
      <div style="height: 45vh; overflow-y: auto">
        <a-form ref="formRef" @keyup.enter="searchQuery" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol">
          <a-row :gutter="4">
            <a-col :lg="12">
              <a-form-item name="companyRegId">
                <j-async-search-select
                  size="middle"
                  placeholder="单位预约"
                  @change="searchQueryDirect"
                  v-model:value="queryParam.companyRegId"
                  dict="company_reg,reg_name,id"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-space>
                <a-button size="middle" type="primary" preIcon="ant-design:search-outlined" @click="searchQuery">查询 </a-button>
              </a-space>
            </a-col>
          </a-row>
        </a-form>
        <a-alert v-if="companyFeeStat" :message="companyFeeStatDesc" type="info" show-icon />
        <a-row :gutter="8">
          <a-col :span="12">
            <a-card size="small" title="分组统计">
              <a-table size="small" :columns="companyFeeStatColumns" :data-source="teamFeeStat" :loading="loading">
                <template #headerCell="{ column }">
                  <template v-if="column.key === 'name'"> </template>
                </template>
              </a-table>
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card size="small" title="项目统计">
              <a-table size="small" :columns="itemFeeStatColumns" :data-source="itemFeeStat" :loading="loading">
                <template #headerCell="{ column }">
                  <template v-if="column.key === 'name'"> </template>
                </template>
              </a-table>
            </a-card>
          </a-col>
        </a-row>
      </div>
    </a-card>

    <!-- 添加标签页切换 -->
    <a-card size="small">
      <a-tabs v-model:activeKey="activeTab" type="card">
        <!-- 支付操作标签页 -->
        <a-tab-pane key="payment" tab="支付操作">
          <template #tab>
            <span>
              <CreditCardOutlined />
              支付操作
            </span>
          </template>

          <div class="payment-info-header" v-if="companyFeeStat">
            <a-space>
              <a-tag>
                <a-typography-text strong>总金额：￥{{ companyFeeStat.totalAmount }}</a-typography-text>
              </a-tag>
              <a-tag color="green">
                <a-typography-text strong>已收金额：￥{{ companyFeeStat.totalPayedAmount }}</a-typography-text>
              </a-tag>
              <a-tag color="red">
                <a-typography-text strong>待收金额：￥{{ companyFeeStat.totalRemainAmount }} </a-typography-text>
              </a-tag>
            </a-space>
          </div>
          <div style="height: 30vh">
            <a-form ref="formRef" class="antd-modal-form" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-row>
                <a-col :span="12">
                  <a-form-item label="支付方式">
                <j-dict-select-tag type="radio" v-model:value="formData.payChannel" dictCode="pay_channel" placeholder="请选择支付方式" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="储值卡号">
                <a-input v-model:value="formData.cardNo" placeholder="请输入储值卡号" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="收款金额">
                <a-input v-model:value="formData.amount" placeholder="请输入收款金额" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="备注">
                <a-textarea v-model:value="formData.remark" placeholder="备注" style="width: 100%" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
        <a-flex justify="right" style="margin-right: 200px">
          <a-space>
            <a-button
              size="middle"
              type="primary"
              @click="openBarcodeModal"
              v-if="
                formData.payChannel == '微信支付' ||
                formData.payChannel == '支付宝' ||
                formData.payChannel == '储值卡' ||
                formData.payChannel == '聚合支付'
              "
              >扫码
            </a-button>
            <a-button
              size="middle"
              type="primary"
              @click="pay"
              :disabled="payLoading || companyFeeStat?.totalRemainAmount == 0"
              :loading="payLoading"
              v-else
              >收费
            </a-button>
            <a-button size="middle" type="dashed" @click="openPayPersonnalRecordModal"> 收费记录 </a-button>
            </a-space>
          </a-flex>
        </div>
        </a-tab-pane>

        <!-- 统计分析标签页 -->
        <a-tab-pane key="statistics" tab="统计分析">
          <template #tab>
            <span>
              <BarChartOutlined />
              统计分析
            </span>
          </template>

          <div v-if="queryParam.companyRegId" style="padding: 20px;">
            <h3>📊 团体支付统计分析</h3>
            <p><strong>单位ID:</strong> {{ queryParam.companyRegId }}</p>
            <p><strong>统计时间:</strong> {{ new Date().toLocaleString() }}</p>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-top: 20px;">
              <div style="padding: 16px; background: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 8px;">
                <h4 style="margin: 0; color: #0ea5e9;">总体检人数</h4>
                <div style="font-size: 24px; font-weight: bold; margin-top: 8px;">
                  {{ companyFeeStat?.totalPersonCount || 0 }}
                </div>
              </div>

              <div style="padding: 16px; background: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px;">
                <h4 style="margin: 0; color: #f59e0b;">总金额</h4>
                <div style="font-size: 24px; font-weight: bold; margin-top: 8px;">
                  ¥{{ companyFeeStat?.totalAmount || 0 }}
                </div>
              </div>

              <div style="padding: 16px; background: #dcfce7; border: 1px solid #22c55e; border-radius: 8px;">
                <h4 style="margin: 0; color: #22c55e;">已收金额</h4>
                <div style="font-size: 24px; font-weight: bold; margin-top: 8px;">
                  ¥{{ companyFeeStat?.totalPayedAmount || 0 }}
                </div>
              </div>

              <div style="padding: 16px; background: #fee2e2; border: 1px solid #ef4444; border-radius: 8px;">
                <h4 style="margin: 0; color: #ef4444;">待收金额</h4>
                <div style="font-size: 24px; font-weight: bold; margin-top: 8px;">
                  ¥{{ companyFeeStat?.totalRemainAmount || 0 }}
                </div>
              </div>
            </div>

            <!-- 导出功能区域 -->
            <div style="margin-top: 20px; padding: 16px; background: #f8fafc; border-radius: 8px;">
              <h4>📤 数据导出</h4>
              <div style="margin-bottom: 16px;">
                <a-space>
                  <a-button type="primary" @click="exportBasicStatistics" :loading="exportLoading.basic">
                    <DownloadOutlined />
                    导出基础统计
                  </a-button>
                  <a-button @click="exportDetailedReport" :loading="exportLoading.detailed">
                    <FileExcelOutlined />
                    导出详细报告
                  </a-button>
                  <a-button @click="exportPaymentRecords" :loading="exportLoading.payment">
                    <TableOutlined />
                    导出支付记录
                  </a-button>
                </a-space>
              </div>

              <!-- 导出选项 -->
              <div style="margin-bottom: 16px; padding: 12px; background: #fafafa; border-radius: 6px; border: 1px solid #d9d9d9;">
                <div style="margin-bottom: 8px; font-weight: 500; color: #666;">⚙️ 导出选项</div>
                <a-row :gutter="16">
                  <a-col :span="8">
                    <div style="margin-bottom: 8px;">
                      <span style="display: inline-block; width: 80px; color: #666;">导出格式：</span>
                      <a-select v-model:value="exportOptions.format" style="width: 100px;" size="small">
                        <a-select-option value="excel">Excel</a-select-option>
                        <a-select-option value="csv">CSV</a-select-option>
                        <a-select-option value="pdf">PDF</a-select-option>
                      </a-select>
                    </div>
                  </a-col>
                  <a-col :span="8">
                    <div style="margin-bottom: 8px;">
                      <span style="display: inline-block; width: 80px; color: #666;">数据脱敏：</span>
                      <a-switch v-model:checked="exportOptions.enableMasking" size="small" />
                      <span style="margin-left: 8px; font-size: 12px; color: #999;">
                        {{ exportOptions.enableMasking ? '开启' : '关闭' }}
                      </span>
                    </div>
                  </a-col>
                  <a-col :span="8">
                    <div style="margin-bottom: 8px;">
                      <span style="display: inline-block; width: 80px; color: #666;">包含替检：</span>
                      <a-switch v-model:checked="exportOptions.includeSubstitute" size="small" />
                      <span style="margin-left: 8px; font-size: 12px; color: #999;">
                        {{ exportOptions.includeSubstitute ? '包含' : '不包含' }}
                      </span>
                    </div>
                  </a-col>
                </a-row>
              </div>

              <div style="margin-top: 16px; font-size: 12px; color: #666;">
                <p>• 支付完成率：{{ paymentCompletionRate }}%</p>
                <p>• 导出功能支持Excel、CSV、PDF格式</p>
                <p>• 可选择是否对敏感信息进行脱敏处理</p>
              </div>
            </div>
          </div>

          <div v-else style="padding: 40px; text-align: center; color: #666;">
            <h4>请先选择单位预约</h4>
            <p>选择单位后即可查看统计分析信息</p>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-card>

    <BarCodeModal ref="barCodeModal" @bar-code-value="barCodeChange" />
    <FeePayPersonnalRecordModal ref="feePayPersonnalRecordModal" />
  </div>
</template>
<script lang="ts" setup name="FeeCompanyPannel">
  import { computed, inject, onMounted, provide, reactive, ref } from 'vue';
  import type { IdcSvr } from '/#/utils';
  import { CompanyFeeStat } from '#/types';
  import { idCardKey } from '@/providekey/provideKeys';
  import { message, theme } from 'ant-design-vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useUserStore } from '@/store/modules/user';
  import { useMethods } from '@/hooks/system/useMethods';
  import JDictSelectTag from '@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import {
    CreditCardOutlined,
    BarChartOutlined,
    DownloadOutlined,
    FileExcelOutlined,
    TableOutlined
  } from '@ant-design/icons-vue';
  import { getCompanyFeeStat, payByHis, payOffline, payOnline } from '@/views/fee/FeePayRecord.api';
  import BarCodeModal from '@/views/fee/BarCodeModal.vue';
  import FeePayPersonnalRecordModal from '@/views/fee/FeePayPersonnalRecordModal.vue';
  import { JAsyncSearchSelect } from '@/components/Form';

  const { createConfirm, createErrorModal } = useMessage();
  const { handleExportXls } = useMethods();

  const { useToken } = theme;
  const { token } = useToken();
  /**单位支付统计*/
  const loading = ref(false);
  const companyFeeStat = ref<CompanyFeeStat>(null);
  const activeTab = ref('payment'); // 添加标签页状态
  const queryParam = reactive({
    companyRegId: '',
  });

  const companyFeeStatColumns = [
    {
      title: '单位名称',
      dataIndex: 'companyName',
      width: 150,
    },
    {
      title: '分组',
      dataIndex: 'teamName',
      width: 100,
    },
    {
      title: '实检人数',
      dataIndex: 'totalCount',
      width: 100,
    },
    {
      title: '实检金额',
      dataIndex: 'totalAmount',
      width: 100,
      customRender: ({ text }) => {
        return `￥${text}`;
      },
    },
    {
      title: '应收金额',
      dataIndex: 'totalAmount',
      width: 100,
      customRender: ({ text }) => {
        return `￥${text}`;
      },
    },
  ];
  const itemFeeStatColumns = [
    {
      title: '项目名称',
      dataIndex: 'itemGroupName',
      width: 150,
    },
    {
      title: '实检人数',
      dataIndex: 'totalCount',
      width: 100,
    },
    {
      title: '实检总金额',
      dataIndex: 'totalAmount',
      width: 100,
      customRender: ({ text }) => {
        return `￥${text}`;
      },
    },
  ];
  const teamFeeStat = computed(() => {
    return companyFeeStat.value?.teamFeeStats || [];
  });
  const itemFeeStat = computed(() => {
    return companyFeeStat.value?.itemGroupFeeStats || [];
  });
  const companyFeeStatDesc = computed(() => {
    return `单位名称：${companyFeeStat.value?.companyName}，总人数：${companyFeeStat.value?.totalCount}，登记人数：${companyFeeStat.value?.totalRegCount}，实检金额：${companyFeeStat.value?.totalAmount}，已收金额：${companyFeeStat.value?.totalPayedAmount}，应收金额：${companyFeeStat.value?.totalRemainAmount}`;
  });

  function searchQueryDirect(e) {
    queryParam.companyRegId = e;
    searchQuery();
  }

  function searchQuery() {
    getCompanyFeeStat(queryParam).then((res) => {
      if (res.success) {
        companyFeeStat.value = res.result;
        formData.amount = companyFeeStat.value.totalRemainAmount;
      }
    });
  }

  const formData = reactive({
    payChannel: '门诊',
    cardNo: '',
    amount: 0,
    remark: '',
  });

  /**体检人员列表部分*/
  const userStore = useUserStore();

  const idcSvr = inject<IdcSvr>('idcSvr', null);
  const idcData = reactive({
    data: {},
    ok: false,
    msg: '',
    state: '',
  });
  const idCard = reactive({ value: idcData.data, setValue: (val) => (idCard.value = val) });
  provide(idCardKey, idCard);

  const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 5 } });
  const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 16 } });

  /**支付*/
  const feePayPersonnalRecordModal = ref(null);
  const payLoading = ref(false);
  const barCodeModal = ref(null);
  const authCode = ref('');

  function verify() {
    if (!companyFeeStat.value) {
      message.error('请选择单位预约记录');
      return false;
    }
    if (!formData.amount) {
      message.error('请输入收款金额');
      return false;
    }
    if (!formData.payChannel) {
      message.error('请选择支付方式');
      return false;
    }
    let remainAmount = companyFeeStat.value?.totalRemainAmount || 0;
    if (formData.amount > remainAmount) {
      message.error('收款金额不能大于待收金额');
      return false;
    }

    return true;
  }

  function openBarcodeModal() {
    if (verify()) {
      barCodeModal.value?.showModal();
    }
  }

  // 获取条码的值
  function barCodeChange(value) {
    authCode.value = value;
    doPayOnline();
  }

  function doPayOnline() {
    let feeRecord = {
      companyRegId: queryParam.companyRegId,
      companyId: companyFeeStat.value.companyId,
      payChannel: formData.payChannel,
      cardNo: formData.cardNo,
      amount: formData.amount,
      remark: formData.remark,
      payerType: '单位支付',
      currency: 'CNY',
      wayCode: 'AUTO_BAR', // 支付方式
      authCode: authCode.value,
    };

    payOnline(feeRecord)
      .then(() => {
        searchQuery();
      })
      .catch(() => {});
  }

  function pay() {
    if (verify()) {
      if (
        formData.payChannel == '微信支付' ||
        formData.payChannel == '支付宝' ||
        formData.payChannel == '储值卡' ||
        formData.payChannel == '聚合支付'
      ) {
        openBarcodeModal();
      } else {
        let data = {
          companyRegId: queryParam.companyRegId,
          companyId: companyFeeStat.value.companyId,
          payChannel: formData.payChannel,
          cardNo: formData.cardNo,
          amount: formData.amount,
          remark: formData.remark,
          payerType: '单位支付',
          currency: 'CNY',
        };
        payLoading.value = true;
        if (formData.payChannel == '现金' || formData.payChannel == '银行卡') {
          payOffline(data)
            .then(() => {
              searchQuery();
            })
            .finally(() => {
              payLoading.value = false;
            });
        } else if (formData.payChannel == '门诊') {
          payByHis(data)
            .then(() => {
              searchQuery();
            })
            .catch(() => {
              payLoading.value = false;
            })
            .finally(() => {
              payLoading.value = false;
            });
        }
      }
    }
  }

  function openPayPersonnalRecordModal() {
    if (!currentReg.value) {
      message.error('请选择体检人员');
      return;
    }
    feePayPersonnalRecordModal.value?.open(currentReg.value);
  }

  function send(cmd) {
    idcSvr.send(cmd);
  }

  // 支付完成率计算
  const paymentCompletionRate = computed(() => {
    const totalAmount = companyFeeStat.value?.totalAmount || 0;
    const payedAmount = companyFeeStat.value?.totalPayedAmount || 0;
    if (totalAmount === 0) return 0;
    return Math.round((payedAmount / totalAmount) * 100);
  });

  // 导出相关状态
  const exportLoading = reactive({
    basic: false,
    detailed: false,
    payment: false
  });

  const exportOptions = reactive({
    format: 'excel',
    enableMasking: true,
    includeSubstitute: true
  });

  // 导出基础统计 - 使用现有的支付记录导出接口
  const exportBasicStatistics = async () => {
    if (!queryParam.companyRegId) {
      createErrorModal({ title: '提示', content: '请先选择单位预约' });
      return;
    }

    exportLoading.basic = true;
    try {
      const companyName = companyFeeStat.value?.companyName || '未知单位';

      // 使用现有的支付记录导出接口，添加单位筛选条件
      await handleExportXls(
        '导出基础统计',
        '/fee/feePayRecord/exportXls',
        {
          companyRegId: queryParam.companyRegId,
          exportType: 'basic',
          enableMasking: exportOptions.enableMasking ? 'true' : 'false'
        },
        `基础统计_${companyName}_${new Date().toLocaleDateString().replace(/\//g, '-')}.xlsx`
      );

    } catch (error) {
      console.error('导出基础统计失败:', error);
      createErrorModal({ title: '导出失败', content: error.message || '导出过程中发生错误' });
    } finally {
      exportLoading.basic = false;
    }
  };

  // 导出详细报告 - 使用现有的支付记录导出接口
  const exportDetailedReport = async () => {
    if (!queryParam.companyRegId) {
      createErrorModal({ title: '提示', content: '请先选择单位预约' });
      return;
    }

    exportLoading.detailed = true;
    try {
      const companyName = companyFeeStat.value?.companyName || '未知单位';

      // 使用现有的支付记录导出接口，添加详细参数
      await handleExportXls(
        '导出详细报告',
        '/fee/feePayRecord/exportXls',
        {
          companyRegId: queryParam.companyRegId,
          exportType: 'detailed',
          enableMasking: exportOptions.enableMasking ? 'true' : 'false',
          includeSubstitute: exportOptions.includeSubstitute ? 'true' : 'false'
        },
        `详细报告_${companyName}_${new Date().toLocaleDateString().replace(/\//g, '-')}.xlsx`
      );

    } catch (error) {
      console.error('导出详细报告失败:', error);
      createErrorModal({ title: '导出失败', content: error.message || '导出过程中发生错误' });
    } finally {
      exportLoading.detailed = false;
    }
  };

  // 导出支付记录 - 使用现有的支付记录导出接口
  const exportPaymentRecords = async () => {
    if (!queryParam.companyRegId) {
      createErrorModal({ title: '提示', content: '请先选择单位预约' });
      return;
    }

    exportLoading.payment = true;
    try {
      const companyName = companyFeeStat.value?.companyName || '未知单位';

      // 直接使用现有的支付记录导出接口
      await handleExportXls(
        '导出支付记录',
        '/fee/feePayRecord/exportXls',
        {
          companyRegId: queryParam.companyRegId,
          exportType: 'payment',
          enableMasking: exportOptions.enableMasking ? 'true' : 'false',
          includeSubstitute: exportOptions.includeSubstitute ? 'true' : 'false'
        },
        `支付记录_${companyName}_${new Date().toLocaleDateString().replace(/\//g, '-')}.xlsx`
      );

    } catch (error) {
      console.error('导出支付记录失败:', error);
      createErrorModal({ title: '导出失败', content: error.message || '导出过程中发生错误' });
    } finally {
      exportLoading.payment = false;
    }
  };


</script>
<style scoped>
  .abandon {
    text-decoration: line-through #ff4d4f;
  }

  /* .active-border {
border: #0a8fe9 1px solid;
}*/
  .active-border {
    animation: glow 800ms ease-out infinite alternate;
  }

  @keyframes glow {
    0% {
      border-color: #0a8fe9;
      box-shadow: 0 0 5px rgba(10, 143, 233, 0.2);
    }
    100% {
      border-color: #0a8fe9;
      box-shadow: 0 0 20px rgba(10, 143, 233, 0.6);
    }
  }

  .error-modal {
    max-height: 50vh;
    overflow-y: scroll;
  }
</style>
