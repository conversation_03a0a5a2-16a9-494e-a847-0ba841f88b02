<template>
  <a-spin :spinning="loading">
    <BasicForm @register="registerForm" />
    <a-affix :offset-bottom="20">
      <div style="display: flex; justify-content: flex-end; background-color: #ffffff; padding: 10px">
        <a-space>
          <a-button preIcon="ant-design:sync-outlined" @click="onReset">重置</a-button>
          <a-button type="primary" preIcon="ant-design:save-filled" @click="onSubmit">保存</a-button>
        </a-space>
      </div>
    </a-affix>
  </a-spin>
</template>

<script lang="ts" setup>
  import { watch, computed, inject, ref, unref, onMounted } from 'vue';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { saveOrUpdateCompanyDepart } from '../CompanyDepart.api';
  import { useCompanyDepartFormSchema, orgTypeOptions } from '../CompanyDepart.data';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { useMessage } from '/@/hooks/web/useMessage';

  const { prefixCls } = useDesign('j-company-depart-form-content');
  const { createMessage } = useMessage();

  const emit = defineEmits(['success']);
  const props = defineProps({
    data: { type: Object, default: () => ({}) },
    rootTreeData: { type: Array, default: () => [] },
  });

  const loading = ref<boolean>(false);
  // 当前是否是更新模式
  const isUpdate = ref<boolean>(true);
  // 当前的表单数据
  const model = ref<object>({});

  //注册表单
  const [registerForm, { resetFields, setFieldsValue, validate, updateSchema }] = useForm({
    schemas: useCompanyDepartFormSchema().basicFormSchema,
    showActionButtonGroup: false,
    baseColProps: { lg: 12, md: 12, xxl: 24 },
  });

  // 监听数据变化
  watch(
    () => props.data,
    async (newData) => {
      if (newData && Object.keys(newData).length > 0) {
        isUpdate.value = !!newData.id;
        model.value = { ...newData };
        
        // 更新上级选择的树数据
        await updateSchema([
          {
            field: 'pid',
            componentProps: {
              treeData: buildTreeSelectData(props.rootTreeData, newData.id),
            },
          },
        ]);

        await setFieldsValue({ ...newData });
      } else {
        // 新增模式
        isUpdate.value = false;
        model.value = {};
        await resetFields();
      }
    },
    { immediate: true, deep: true }
  );

  // 监听根树数据变化
  watch(
    () => props.rootTreeData,
    async (newTreeData) => {
      if (newTreeData && newTreeData.length > 0) {
        await updateSchema([
          {
            field: 'pid',
            componentProps: {
              treeData: buildTreeSelectData(newTreeData, model.value?.id),
            },
          },
        ]);
      }
    },
    { immediate: true, deep: true }
  );

  // 构建树选择数据（排除自己和子级）
  function buildTreeSelectData(treeData: any[], excludeId?: string): any[] {
    if (!treeData || !Array.isArray(treeData)) return [];
    
    return treeData
      .filter(item => item.id !== excludeId)
      .map(item => ({
        title: item.name || item.title,
        value: item.id,
        key: item.id,
        children: item.children ? buildTreeSelectData(item.children, excludeId) : undefined,
      }));
  }

  // 重置表单
  async function onReset() {
    await resetFields();
    if (model.value && Object.keys(model.value).length > 0) {
      await setFieldsValue({ ...model.value });
    }
  }

  // 提交事件
  async function onSubmit() {
    try {
      loading.value = true;
      let values = await validate();
      
      // 合并原始数据和表单数据
      values = Object.assign({}, model.value, values);
      
      // 数据处理
      if (!values.pid || values.pid === '') {
        values.pid = '0'; // 根节点
      }
      
      // 根据类型设置默认值
      if (values.orgType === '1') {
        // 单位类型
        values.hasChild = '0'; // 默认无子节点
      } else if (values.orgType === '2') {
        // 部门类型
        values.hasChild = '0'; // 默认无子节点
      }

      //提交表单
      await saveOrUpdateCompanyDepart(values, isUpdate.value);
      createMessage.success(isUpdate.value ? '更新成功' : '保存成功');
      
      //刷新列表
      emit('success');
      
      // 更新本地数据
      Object.assign(model.value, values);
    } catch (error) {
      console.error('保存失败:', error);
    } finally {
      loading.value = false;
    }
  }

  // 获取当前节点类型显示名称
  const currentTypeName = computed(() => {
    if (!model.value?.orgType) return '';
    return model.value.orgType === '1' ? '单位' : '部门';
  });

  // 获取当前节点状态显示
  const currentStatus = computed(() => {
    if (!model.value?.id) return '新增';
    return isUpdate.value ? '编辑' : '新增';
  });
</script>

<style lang="less" scoped>
  .j-company-depart-form-content {
    .ant-form {
      padding: 20px;
    }
  }
</style>
