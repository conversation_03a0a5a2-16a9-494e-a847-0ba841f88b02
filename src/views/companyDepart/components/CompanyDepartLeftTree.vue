<template>
  <a-card :bordered="false" style="height: 100%">
    <div class="j-table-operator" style="width: 100%">
      <a-button type="primary" preIcon="ant-design:plus-outlined" @click="onAddCompany">新增单位</a-button>
      <a-button type="primary" preIcon="ant-design:plus-outlined" @click="onAddChildDepart()">添加下级</a-button>
      <a-upload name="file" :showUploadList="false" :customRequest="onImportXls">
        <a-button type="primary" preIcon="ant-design:import-outlined">导入</a-button>
      </a-upload>
      <a-button type="primary" preIcon="ant-design:export-outlined" @click="onExportXls">导出</a-button>
      <a-button
        type="primary"
        preIcon="ant-design:delete-outlined"
        :disabled="checkedKeys.length === 0"
        @click="onBatchDelete"
      >
        批量删除
      </a-button>
    </div>

    <a-divider style="margin: 10px 0" />

    <a-spin :spinning="loading">
      <a-input-search placeholder="按名称搜索…" style="margin-bottom: 10px" @search="onSearch" />
      <!--单位部门树-->
      <template v-if="treeData.length > 0">
        <a-tree
          v-if="!treeReloading"
          checkable
          :clickRowToExpand="false"
          :treeData="treeData"
          :selectedKeys="selectedKeys"
          :checkStrictly="checkStrictly"
          :load-data="loadChildrenTreeData"
          :checkedKeys="checkedKeys"
          v-model:expandedKeys="expandedKeys"
          @check="onCheck"
          @select="onSelect"
        >
          <template #title="{ key: treeKey, title, dataRef }">
            <a-dropdown :trigger="['contextmenu']">
              <Popconfirm
                :open="visibleTreeKey === treeKey"
                title="确定要删除吗？"
                ok-text="确定"
                cancel-text="取消"
                placement="rightTop"
                @confirm="onDelete(dataRef)"
                @open-change="onVisibleChange"
              >
                <span>
                  <Icon 
                    :icon="getNodeIcon(dataRef)" 
                    style="margin-right: 5px" 
                    :style="{ color: getNodeColor(dataRef) }"
                  />
                  {{ title }}
                </span>
              </Popconfirm>

              <template #overlay>
                <a-menu @click="">
                  <a-menu-item key="1" @click="onAddChildDepart(dataRef)">
                    添加{{ getChildTypeName(dataRef) }}
                  </a-menu-item>
                  <a-menu-item key="2" @click="visibleTreeKey = treeKey">
                    <span style="color: red">删除</span>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </template>
        </a-tree>
      </template>
      <a-empty v-else description="暂无数据" />
    </a-spin>
    <CompanyDepartFormModal :rootTreeData="treeData" @register="registerModal" @success="loadRootTreeData" />
  </a-card>
</template>

<script lang="ts" setup>
  import { inject, nextTick, ref, unref } from 'vue';
  import { useModal } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useMethods } from '/@/hooks/system/useMethods';
  import { Icon } from '/@/components/Icon';
  import { Popconfirm } from 'ant-design-vue';
  import {
    queryCompanyDepartTreeSync,
    deleteCompanyDepart,
    batchDeleteCompanyDepart,
    searchCompanyDepartByKeywords,
    Api as CompanyDepartApi
  } from '../CompanyDepart.api';
  import CompanyDepartFormModal from './CompanyDepartFormModal.vue';

  const prefixCls = inject('prefixCls');
  const emit = defineEmits(['select', 'rootTreeData']);
  const { createMessage } = useMessage();
  const { handleImportXls, handleExportXls } = useMethods();

  const loading = ref<boolean>(false);
  // 单位部门树列表数据
  const treeData = ref<any[]>([]);
  // 当前选中的项
  const checkedKeys = ref<any[]>([]);
  // 当前展开的项
  const expandedKeys = ref<any[]>([]);
  // 当前选中的项
  const selectedKeys = ref<any[]>([]);
  // 树组件重新加载
  const treeReloading = ref<boolean>(false);
  // 树父子是否关联
  const checkStrictly = ref<boolean>(true);
  // 当前选中的单位/部门
  const currentCompanyDepart = ref<any>(null);
  // 控制确认删除提示框是否显示
  const visibleTreeKey = ref<any>(null);
  // 搜索关键字
  const searchKeyword = ref('');

  // 注册 modal
  const [registerModal, { openModal }] = useModal();

  // 初始化加载数据
  loadRootTreeData();

  // 获取节点图标
  function getNodeIcon(node: any) {
    if (node.orgType === '1' || node.orgType === 'company') {
      return 'ant-design:bank-outlined'; // 单位图标
    } else {
      return 'ant-design:apartment-outlined'; // 部门图标
    }
  }

  // 获取节点颜色
  function getNodeColor(node: any) {
    if (node.orgType === '1' || node.orgType === 'company') {
      return '#1890ff'; // 单位蓝色
    } else {
      return '#52c41a'; // 部门绿色
    }
  }

  // 获取子级类型名称
  function getChildTypeName(node: any) {
    if (node.orgType === '1' || node.orgType === 'company') {
      return '部门';
    } else {
      return '子部门';
    }
  }

  // 加载根节点数据
  async function loadRootTreeData() {
    try {
      loading.value = true;
      treeData.value = [];
      let result = await queryCompanyDepartTreeSync();
      if (Array.isArray(result)) {
        treeData.value = result;
        emit('rootTreeData', result);
      }
      autoExpandParentNode();
    } finally {
      loading.value = false;
    }
  }

  // 异步加载子节点数据
  async function loadChildrenTreeData(treeNode) {
    try {
      const result = await queryCompanyDepartTreeSync({ pid: treeNode.dataRef.id });
      if (Array.isArray(result)) {
        treeNode.dataRef.children = result;
      }
    } catch (error) {
      console.error('加载子节点失败:', error);
    }
  }

  // 自动展开父节点
  function autoExpandParentNode() {
    if (searchKeyword.value) {
      nextTick(() => {
        expandedKeys.value = getAllParentKeys(treeData.value);
      });
    }
  }

  // 获取所有父节点的key
  function getAllParentKeys(treeData: any[], keys: any[] = []): any[] {
    treeData.forEach((item) => {
      if (item.children && item.children.length > 0) {
        keys.push(item.key || item.id);
        getAllParentKeys(item.children, keys);
      }
    });
    return keys;
  }

  // 树节点选择事件
  function onSelect(selectedKeys: any[], { selected, selectedNodes, node, event }) {
    if (selected && selectedNodes.length > 0) {
      const selectedNode = selectedNodes[0];
      currentCompanyDepart.value = selectedNode;
      emit('select', selectedNode);
    }
  }

  // 删除节点
  async function onDelete(node) {
    try {
      await deleteCompanyDepart(node.id);
      createMessage.success('删除成功');
      await loadRootTreeData();
    } catch (error) {
      console.error('删除失败:', error);
    }
    visibleTreeKey.value = null;
  }

  // 控制删除确认框显示
  function onVisibleChange(visible: boolean) {
    if (!visible) {
      visibleTreeKey.value = null;
    }
  }

  // 新增单位
  function onAddCompany() {
    const record = { orgType: '1', pid: '0' }; // 单位类型，根节点
    openModal(true, { isUpdate: false, isChild: false, record });
  }

  // 添加子级部门
  function onAddChildDepart(data = currentCompanyDepart.value) {
    if (data == null) {
      createMessage.warning('请先选择一个单位或部门');
      return;
    }
    const record = { 
      pid: data.id, 
      orgType: '2' // 部门类型
    };
    openModal(true, { isUpdate: false, isChild: true, record });
  }

  // 搜索事件
  async function onSearch(value: string) {
    if (value) {
      try {
        loading.value = true;
        treeData.value = [];
        let result = await searchCompanyDepartByKeywords({ keyWord: value });
        if (Array.isArray(result)) {
          treeData.value = result;
        }
        autoExpandParentNode();
      } finally {
        loading.value = false;
      }
    } else {
      loadRootTreeData();
    }
    searchKeyword.value = value;
  }

  // 树复选框选择事件
  function onCheck(e) {
    if (Array.isArray(e)) {
      checkedKeys.value = e;
    } else {
      checkedKeys.value = e.checked;
    }
  }

  // 批量删除
  async function onBatchDelete() {
    if (checkedKeys.value.length === 0) {
      createMessage.warning('请选择要删除的数据');
      return;
    }
    try {
      await batchDeleteCompanyDepart(checkedKeys.value);
      createMessage.success('批量删除成功');
      checkedKeys.value = [];
      await loadRootTreeData();
    } catch (error) {
      console.error('批量删除失败:', error);
    }
  }

  // 导入
  function onImportXls(info) {
    handleImportXls(info, CompanyDepartApi.importExcelUrl, loadRootTreeData);
  }

  // 导出
  function onExportXls() {
    handleExportXls('单位部门数据', CompanyDepartApi.exportXlsUrl);
  }

  // 暴露方法给父组件
  defineExpose({
    loadRootTreeData,
  });
</script>

<style lang="less" scoped>
  .j-table-operator {
    margin-bottom: 10px;
    
    .ant-btn {
      margin-right: 8px;
      margin-bottom: 8px;
    }
  }
</style>
