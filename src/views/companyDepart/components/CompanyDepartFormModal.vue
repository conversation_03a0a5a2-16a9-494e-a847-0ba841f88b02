<template>
  <BasicModal :title="title" :width="'70%'" v-bind="$attrs" @ok="handleOk" @register="registerModal">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
  import { watch, computed, inject, ref, unref, onMounted } from 'vue';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { saveOrUpdateCompanyDepart, getNextSort } from '../CompanyDepart.api';
  import { useCompanyDepartFormSchema, orgTypeOptions, getChildTypeOptions } from '../CompanyDepart.data';
  import { useMessage } from '/@/hooks/web/useMessage';

  const emit = defineEmits(['success', 'register']);
  const props = defineProps({
    rootTreeData: { type: Array, default: () => [] },
  });

  const { createMessage } = useMessage();
  const prefixCls = inject('prefixCls');
  
  // 当前是否是更新模式
  const isUpdate = ref<boolean>(false);
  // 当前的弹窗数据
  const model = ref<object>({});
  const title = computed(() => (isUpdate.value ? '编辑' : '新增'));

  //注册表单
  const [registerForm, { resetFields, setFieldsValue, validate, updateSchema }] = useForm({
    schemas: useCompanyDepartFormSchema().basicFormSchema,
    showActionButtonGroup: false,
    baseColProps: { lg: 12, md: 12, xxl: 12 },
    labelCol: { span: 6 },
  });

  // 注册弹窗
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    await resetFields();
    isUpdate.value = unref(data?.isUpdate);
    
    // 当前是否为添加子级
    let isChild = unref(data?.isChild);
    let parentData = null;
    
    // 构建树选择数据
    const treeSelectData = buildTreeSelectData(props.rootTreeData);
    
    // 更新表单配置
    await updateSchema([
      {
        field: 'pid',
        show: true,
        componentProps: {
          disabled: isChild, // 如果是添加子级，禁用上级选择
          treeData: treeSelectData,
        },
      },
      {
        field: 'orgType',
        componentProps: { 
          options: isChild && data?.record?.pid ? 
            getChildTypeOptions(getParentType(data.record.pid)) : 
            orgTypeOptions 
        },
      },
    ]);

    let record = unref(data?.record);
    if (typeof record !== 'object') {
      record = {};
    }

    // 设置默认值
    if (!isUpdate.value) {
      // 新增时的默认值
      record = Object.assign(
        {
          orgType: isChild ? '2' : '1', // 子级默认为部门，根级默认为单位
          pid: isChild ? record.pid : '0',
        },
        record
      );
    }

    model.value = record;
    await setFieldsValue({ ...record });
  });

  // 构建树选择数据
  function buildTreeSelectData(treeData: any[]): any[] {
    if (!treeData || !Array.isArray(treeData)) return [];
    
    return treeData.map(item => ({
      title: item.name || item.title,
      value: item.id,
      key: item.id,
      children: item.children ? buildTreeSelectData(item.children) : undefined,
    }));
  }

  // 获取父级类型
  function getParentType(pid: string): string {
    if (!pid || pid === '0') return '1';
    
    // 递归查找父级类型
    function findParentType(treeData: any[], targetId: string): string {
      for (const item of treeData) {
        if (item.id === targetId) {
          return item.orgType || '1';
        }
        if (item.children && item.children.length > 0) {
          const result = findParentType(item.children, targetId);
          if (result) return result;
        }
      }
      return '1';
    }
    
    return findParentType(props.rootTreeData, pid);
  }

  // 提交事件
  async function handleOk() {
    try {
      setModalProps({ confirmLoading: true });
      let values = await validate();
      
      // 合并数据
      values = Object.assign({}, model.value, values);
      
      // 数据处理
      if (!values.pid || values.pid === '') {
        values.pid = '0';
      }
      
      // 设置默认值
      values.hasChild = '0'; // 新增节点默认无子节点
      
      //提交表单
      await saveOrUpdateCompanyDepart(values, isUpdate.value);
      createMessage.success(isUpdate.value ? '更新成功' : '添加成功');
      
      //关闭弹窗
      closeModal();
      //刷新列表
      emit('success');
    } catch (error) {
      console.error('保存失败:', error);
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>

<style lang="less" scoped>
  .ant-form {
    padding: 20px;
  }
</style>
