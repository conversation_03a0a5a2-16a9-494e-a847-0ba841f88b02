<template>
  <a-spin :spinning="loading">
    <template v-if="treeData.length > 0">
      <BasicTree
        ref="basicTree"
        class="company-depart-rule-tree"
        checkable
        :treeData="treeData"
        :checkedKeys="checkedKeys"
        :selectedKeys="selectedKeys"
        :expandedKeys="expandedKeys"
        :checkStrictly="checkStrictly"
        style="height: 500px; overflow: auto"
        @check="onCheck"
        @expand="onExpand"
        @select="onSelect"
      >
        <template #title="{ slotTitle, ruleFlag }">
          <span>{{ slotTitle }}</span>
          <Icon v-if="ruleFlag" icon="ant-design:align-left-outlined" style="margin-left: 5px; color: red" />
        </template>
      </BasicTree>
    </template>
    <a-empty v-else description="无可配置权限" />

    <a-affix :offset-bottom="20">
      <div style="display: flex; justify-content: flex-end; background-color: #ffffff; padding: 10px">
        <a-space>
          <a-button @click="toggleExpandAll(false)">收起全部</a-button>
          <a-button @click="toggleExpandAll(true)">展开全部</a-button>
          <a-button @click="onReset">重置</a-button>
          <a-button type="primary" @click="onSubmit">保存权限</a-button>
        </a-space>
      </div>
    </a-affix>
  </a-spin>
</template>

<script lang="ts" setup>
  import { watch, computed, inject, ref, nextTick } from 'vue';
  import { BasicTree } from '/@/components/Tree/index';
  import { Icon } from '/@/components/Icon';
  import { 
    queryRoleTreeList, 
    queryCompanyDepartPermission, 
    saveCompanyDepartPermission 
  } from '../CompanyDepart.api';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { useMessage } from '/@/hooks/web/useMessage';

  const { prefixCls } = useDesign('j-company-depart-rule-content');
  const { createMessage } = useMessage();

  const props = defineProps({
    data: { type: Object, default: () => ({}) },
  });

  // 当前选中的单位/部门ID，可能会为空，代表未选择
  const companyDepartId = computed(() => props.data?.id);
  const companyDepartType = computed(() => props.data?.orgType);

  const basicTree = ref();
  const loading = ref<boolean>(false);
  const treeData = ref<any[]>([]);
  const expandedKeys = ref<Array<any>>([]);
  const selectedKeys = ref<Array<any>>([]);
  const checkedKeys = ref<Array<any>>([]);
  const lastCheckedKeys = ref<Array<any>>([]);
  const checkStrictly = ref(true);

  // 初始化加载数据
  loadData();
  watch(companyDepartId, () => loadCompanyDepartPermission(), { immediate: true });

  async function loadData() {
    try {
      loading.value = true;
      let { treeList } = await queryRoleTreeList();
      treeData.value = treeList;
      await nextTick();
      toggleExpandAll(true);
    } finally {
      loading.value = false;
    }
  }

  async function loadCompanyDepartPermission() {
    if (companyDepartId.value) {
      try {
        loading.value = true;
        // 使用部门权限接口，传入单位/部门ID
        let keys = await queryCompanyDepartPermission({ 
          departId: companyDepartId.value 
        });
        checkedKeys.value = keys;
        lastCheckedKeys.value = [...keys];
      } finally {
        loading.value = false;
      }
    } else {
      // 清空权限选择
      checkedKeys.value = [];
      lastCheckedKeys.value = [];
    }
  }

  async function onSubmit() {
    if (!companyDepartId.value) {
      createMessage.warning('请先选择要配置权限的单位或部门');
      return;
    }

    try {
      loading.value = true;
      await saveCompanyDepartPermission({
        departId: companyDepartId.value,
        permissionIds: checkedKeys.value.join(','),
        lastpermissionIds: lastCheckedKeys.value.join(','),
      });
      createMessage.success('权限保存成功');
      await loadData();
      await loadCompanyDepartPermission();
    } finally {
      loading.value = false;
    }
  }

  // 重置权限
  async function onReset() {
    checkedKeys.value = [...lastCheckedKeys.value];
  }

  // tree勾选复选框事件
  function onCheck(event) {
    if (!Array.isArray(event)) {
      checkedKeys.value = event.checked;
    } else {
      checkedKeys.value = event;
    }
  }

  // tree展开事件
  function onExpand(event) {
    expandedKeys.value = event;
  }

  // tree选择事件
  function onSelect(event) {
    selectedKeys.value = event;
  }

  // 展开/收起全部
  function toggleExpandAll(expanded: boolean) {
    if (expanded) {
      expandedKeys.value = getAllKeys(treeData.value);
    } else {
      expandedKeys.value = [];
    }
  }

  // 获取所有节点的key
  function getAllKeys(treeData: any[], keys: any[] = []): any[] {
    treeData.forEach((item) => {
      keys.push(item.key || item.id);
      if (item.children && item.children.length > 0) {
        getAllKeys(item.children, keys);
      }
    });
    return keys;
  }

  // 获取当前配置对象的显示名称
  const currentTargetName = computed(() => {
    if (!props.data?.name) return '';
    const typeName = companyDepartType.value === '1' ? '单位' : '部门';
    return `${props.data.name}（${typeName}）`;
  });
</script>

<style lang="less" scoped>
  .company-depart-rule-tree {
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    padding: 10px;
  }

  .j-company-depart-rule-content {
    .ant-tree {
      background: #fafafa;
    }
  }
</style>
