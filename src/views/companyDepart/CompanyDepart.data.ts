import { FormSchema } from '/@/components/Form';
import { BasicColumn } from '/@/components/Table';

// 单位部门基础表单配置
export function useCompanyDepartFormSchema() {
  const basicFormSchema: FormSchema[] = [
    {
      field: 'orgType',
      label: '类型',
      component: 'RadioButtonGroup',
      componentProps: { 
        options: [
          { value: '1', label: '单位' },
          { value: '2', label: '部门' }
        ]
      },
      rules: [{ required: true, message: '请选择类型' }],
      span: 12,
    },
    {
      field: 'pid',
      label: '上级单位/部门',
      component: 'TreeSelect',
      componentProps: {
        treeData: [],
        placeholder: '请选择上级单位/部门',
        dropdownStyle: { maxHeight: '200px', overflow: 'auto' },
        allowClear: true,
      },
      span: 12,
    },
    {
      field: 'orgCode',
      label: '编码',
      component: 'Input',
      componentProps: {
        placeholder: '请输入编码',
      },
      span: 12,
    },
    {
      field: 'name',
      label: '名称',
      component: 'Input',
      componentProps: {
        placeholder: '请输入名称',
      },
      rules: [
        { required: true, message: '名称不能为空' },
        { max: 100, message: '长度不能超过100个字符' }
      ],
      span: 12,
    },
    {
      field: 'shortName',
      label: '简称',
      component: 'Input',
      componentProps: {
        placeholder: '请输入简称',
      },
      span: 12,
    },
    {
      field: 'helpChar',
      label: '助记码',
      component: 'Input',
      componentProps: {
        placeholder: '请输入助记码',
      },
      span: 12,
    },
    // 单位特有字段
    {
      field: 'creditCode',
      label: '统一社会信用代码',
      component: 'Input',
      componentProps: {
        placeholder: '请输入统一社会信用代码',
      },
      ifShow: ({ values }) => values.orgType === '1',
      span: 12,
    },
    {
      field: 'economicType',
      label: '经济类型',
      component: 'JDictSelectTag',
      componentProps: {
        dictCode: 'economic_type',
        placeholder: '请选择经济类型',
      },
      ifShow: ({ values }) => values.orgType === '1',
      span: 12,
    },
    {
      field: 'indutry',
      label: '行业',
      component: 'Input',
      componentProps: {
        placeholder: '请输入行业',
      },
      ifShow: ({ values }) => values.orgType === '1',
      span: 12,
    },
    {
      field: 'enSize',
      label: '企业规模',
      component: 'Input',
      componentProps: {
        placeholder: '请输入企业规模',
      },
      ifShow: ({ values }) => values.orgType === '1',
      span: 12,
    },
    // 部门特有字段
    {
      field: 'hisCode',
      label: 'HIS系统编码',
      component: 'Input',
      componentProps: {
        placeholder: '请输入HIS系统编码',
      },
      ifShow: ({ values }) => values.orgType === '2',
      span: 12,
    },
    {
      field: 'departFunCategory',
      label: '科室功能类别',
      component: 'JDictSelectTag',
      componentProps: {
        dictCode: 'depart_fun_category',
        placeholder: '请选择科室类别',
      },
      ifShow: ({ values }) => values.orgType === '2',
      defaultValue: '检查',
      span: 12,
    },
    {
      field: 'sexLimit',
      label: '性别限制',
      component: 'JDictSelectTag',
      componentProps: {
        dictCode: 'sexLimit',
        placeholder: '请选择性别限制',
      },
      ifShow: ({ values }) => values.orgType === '2',
      defaultValue: '不限',
      span: 12,
    },
    {
      field: 'maxPerDay',
      label: '日检能力',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入日检能力',
      },
      suffix: '人次',
      ifShow: ({ values }) => values.orgType === '2',
      span: 12,
    },
    // 通用字段
    {
      field: 'phone',
      label: '联系电话',
      component: 'Input',
      componentProps: {
        placeholder: '请输入联系电话',
      },
      span: 12,
    },
    {
      field: 'fax',
      label: '传真',
      component: 'Input',
      componentProps: {
        placeholder: '请输入传真',
      },
      span: 12,
    },
    {
      field: 'address',
      label: '地址',
      component: 'InputTextArea',
      componentProps: {
        placeholder: '请输入地址',
        rows: 2,
      },
      span: 24,
    },
    {
      field: 'memo',
      label: '备注',
      component: 'InputTextArea',
      componentProps: {
        placeholder: '请输入备注',
        rows: 3,
      },
      span: 24,
    },
  ];
  return { basicFormSchema };
}

// 表格列配置
export const columns: BasicColumn[] = [
  {
    title: '名称',
    dataIndex: 'name',
    width: 200,
    align: 'left',
  },
  {
    title: '类型',
    dataIndex: 'orgType',
    width: 80,
    customRender: ({ text }) => {
      return text === '1' ? '单位' : '部门';
    },
  },
  {
    title: '编码',
    dataIndex: 'orgCode',
    width: 120,
  },
  {
    title: '简称',
    dataIndex: 'shortName',
    width: 150,
  },
  {
    title: '联系电话',
    dataIndex: 'phone',
    width: 120,
  },
  {
    title: '地址',
    dataIndex: 'address',
    width: 200,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 150,
  },
];

// 机构类型选项
export const orgTypeOptions = [
  { value: '1', label: '单位' },
  { value: '2', label: '部门' },
];

// 单位类型选项（用于单位下的子级）
export const companyChildOptions = [
  { value: '2', label: '部门' },
];

// 部门类型选项（用于部门下的子级）
export const departChildOptions = [
  { value: '2', label: '子部门' },
];

// 获取子级类型选项
export function getChildTypeOptions(parentType: string) {
  if (parentType === '1') {
    return companyChildOptions;
  } else {
    return departChildOptions;
  }
}

// 表单验证规则
export const formRules = {
  name: [
    { required: true, message: '请输入名称' },
    { max: 100, message: '名称长度不能超过100个字符' }
  ],
  orgType: [
    { required: true, message: '请选择类型' }
  ],
  orgCode: [
    { max: 50, message: '编码长度不能超过50个字符' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
  ],
};
