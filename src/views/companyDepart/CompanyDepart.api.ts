import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';

const { createConfirm } = useMessage();

export enum Api {
  // 基于Company的API接口
  queryTreeSync = '/basicinfo/company/loadTreeRoot',
  queryTreeChildren = '/basicinfo/company/loadTreeChildren',
  save = '/basicinfo/company/add',
  edit = '/basicinfo/company/edit',
  delete = '/basicinfo/company/delete',
  deleteBatch = '/basicinfo/company/deleteBatch',
  exportXlsUrl = '/basicinfo/company/exportXls',
  importExcelUrl = '/basicinfo/company/importExcel',
  searchCompany = '/basicinfo/company/searchCompany',

  // 权限相关API（复用部门权限接口）
  roleQueryTreeList = '/sys/role/queryTreeList',
  queryPermission = '/sys/permission/queryDepartPermission',
  savePermission = '/sys/permission/saveDepartPermission',
  dataRule = '/sys/sysDepartPermission/datarule',
}

/**
 * 获取单位部门树列表
 */
export const queryCompanyDepartTreeSync = (params?) => {
  // 如果有pid参数，使用loadTreeChildren接口
  if (params?.pid) {
    return defHttp.get({ url: Api.queryTreeChildren, params: { pid: params.pid } });
  }
  // 否则加载根节点数据
  return defHttp.get({
    url: Api.queryTreeSync,
    params: { async: true, pcode: '' }
  });
};

/**
 * 异步加载树形子节点数据
 */
export const loadTreeChildren = (params?) => {
  return defHttp.get({ url: Api.queryTreeChildren, params });
};

/**
 * 保存或者更新单位部门
 */
export const saveOrUpdateCompanyDepart = (params, isUpdate) => {
  if (isUpdate) {
    return defHttp.put({ url: Api.edit, params });
  } else {
    return defHttp.post({ url: Api.save, params });
  }
};

/**
 * 删除单位部门
 */
export const deleteCompanyDepart = (id: string) => {
  return defHttp.delete({ url: Api.delete, params: { id } }, { joinParamsToUrl: true });
};

/**
 * 批量删除单位部门
 */
export const batchDeleteCompanyDepart = (ids: string[]) => {
  return new Promise((resolve, reject) => {
    createConfirm({
      iconType: 'warning',
      title: '确认删除',
      content: '是否删除选中数据',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        return defHttp.delete(
          { url: Api.deleteBatch, data: ids }, 
          { joinParamsToUrl: true }
        ).then(() => {
          resolve(true);
        }).catch(reject);
      },
      onCancel: () => {
        reject(new Error('用户取消操作'));
      }
    });
  });
};

/**
 * 根据关键字搜索单位部门
 */
export const searchCompanyDepartByKeywords = (params) => {
  return defHttp.get({ url: Api.searchCompany, params });
};

/**
 * 查询角色权限树列表
 */
export const queryRoleTreeList = () => {
  return defHttp.get({ url: Api.roleQueryTreeList });
};

/**
 * 查询单位部门权限
 */
export const queryCompanyDepartPermission = (params) => {
  return defHttp.get({ url: Api.queryPermission, params });
};

/**
 * 保存单位部门权限
 */
export const saveCompanyDepartPermission = (params) => {
  return defHttp.post({ url: Api.savePermission, params });
};

/**
 * 保存单位部门数据权限
 */
export const saveCompanyDepartDataRule = (params) => {
  return defHttp.post({ url: Api.dataRule, params });
};

/**
 * 根据ID查询单位部门详情
 */
export const getCompanyDepartById = (id: string) => {
  return defHttp.get({ url: '/basicinfo/company/queryById', params: { id } });
};

/**
 * 获取下一个排序号
 */
export const getNextSort = (params?) => {
  return defHttp.get({ url: '/basicinfo/company/getNextSort', params });
};

/**
 * 导出Excel
 */
export const exportCompanyDepartXls = (params) => {
  return defHttp.get({ url: Api.exportXlsUrl, params }, { responseType: 'blob' });
};

/**
 * 导入Excel
 */
export const importCompanyDepartExcel = (params) => {
  return defHttp.post({ url: Api.importExcelUrl, params });
};
