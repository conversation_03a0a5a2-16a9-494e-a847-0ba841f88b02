<template>
  <div class="p-4">
    <h2>单位部门管理功能测试</h2>
    
    <a-card title="API接口测试" class="mb-4">
      <a-space direction="vertical" style="width: 100%">
        <a-button type="primary" @click="testLoadTreeData">测试加载树形数据</a-button>
        <a-button type="primary" @click="testSearchData">测试搜索功能</a-button>
        <a-button type="primary" @click="testPermissionData">测试权限数据</a-button>
      </a-space>
      
      <a-divider />
      
      <div v-if="testResult">
        <h4>测试结果：</h4>
        <pre>{{ JSON.stringify(testResult, null, 2) }}</pre>
      </div>
    </a-card>

    <a-card title="组件功能测试">
      <a-tabs>
        <a-tab-pane key="tree" tab="树形组件测试">
          <CompanyDepartLeftTree ref="leftTreeRef" @select="onTreeSelect" @root-tree-data="onRootTreeData" />
        </a-tab-pane>
        
        <a-tab-pane key="form" tab="表单组件测试">
          <CompanyDepartFormTab 
            v-if="selectedData" 
            :data="selectedData" 
            :rootTreeData="rootTreeData" 
            @success="onFormSuccess" 
          />
          <a-empty v-else description="请先从左侧树选择一个节点" />
        </a-tab-pane>
        
        <a-tab-pane key="permission" tab="权限组件测试">
          <CompanyDepartRuleTab 
            v-if="selectedData" 
            :data="selectedData" 
          />
          <a-empty v-else description="请先从左侧树选择一个节点" />
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import CompanyDepartLeftTree from './components/CompanyDepartLeftTree.vue';
  import CompanyDepartFormTab from './components/CompanyDepartFormTab.vue';
  import CompanyDepartRuleTab from './components/CompanyDepartRuleTab.vue';
  import { 
    queryCompanyDepartTreeSync, 
    searchCompanyDepartByKeywords,
    queryRoleTreeList 
  } from './CompanyDepart.api';

  const { createMessage } = useMessage();

  const testResult = ref(null);
  const selectedData = ref(null);
  const rootTreeData = ref([]);
  const leftTreeRef = ref();

  // 测试加载树形数据
  async function testLoadTreeData() {
    try {
      const result = await queryCompanyDepartTreeSync();
      testResult.value = {
        type: '加载树形数据',
        success: true,
        data: result,
        count: Array.isArray(result) ? result.length : 0
      };
      createMessage.success('树形数据加载成功');
    } catch (error) {
      testResult.value = {
        type: '加载树形数据',
        success: false,
        error: error.message
      };
      createMessage.error('树形数据加载失败: ' + error.message);
    }
  }

  // 测试搜索功能
  async function testSearchData() {
    try {
      const result = await searchCompanyDepartByKeywords({ keyWord: '测试' });
      testResult.value = {
        type: '搜索功能',
        success: true,
        data: result,
        count: Array.isArray(result) ? result.length : 0
      };
      createMessage.success('搜索功能测试成功');
    } catch (error) {
      testResult.value = {
        type: '搜索功能',
        success: false,
        error: error.message
      };
      createMessage.error('搜索功能测试失败: ' + error.message);
    }
  }

  // 测试权限数据
  async function testPermissionData() {
    try {
      const result = await queryRoleTreeList();
      testResult.value = {
        type: '权限数据',
        success: true,
        data: result,
        hasTreeList: !!result.treeList,
        treeCount: result.treeList ? result.treeList.length : 0
      };
      createMessage.success('权限数据加载成功');
    } catch (error) {
      testResult.value = {
        type: '权限数据',
        success: false,
        error: error.message
      };
      createMessage.error('权限数据加载失败: ' + error.message);
    }
  }

  // 树选择事件
  function onTreeSelect(data) {
    selectedData.value = data;
    createMessage.info(`选中节点: ${data.name || data.title}`);
  }

  // 根树数据事件
  function onRootTreeData(data) {
    rootTreeData.value = data;
  }

  // 表单成功事件
  function onFormSuccess() {
    createMessage.success('表单操作成功');
    leftTreeRef.value?.loadRootTreeData();
  }
</script>

<style lang="less" scoped>
  pre {
    background: #f5f5f5;
    padding: 10px;
    border-radius: 4px;
    max-height: 300px;
    overflow: auto;
  }
</style>
