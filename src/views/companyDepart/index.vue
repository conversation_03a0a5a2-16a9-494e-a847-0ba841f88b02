<template>
  <a-row :class="['p-4', `${prefixCls}--box`]" type="flex" :gutter="10">
    <a-col :xl="8" :lg="24" :md="8" style="margin-bottom: 10px">
      <CompanyDepartLeftTree ref="leftTree" @select="onTreeSelect" @root-tree-data="onRootTreeData" />
    </a-col>
    <a-col :xl="16" :lg="16" :md="16" style="margin-bottom: 10px">
      <div style="height: 100%" :class="[`${prefixCls}`]">
        <a-tabs v-show="companyDepartData != null" defaultActiveKey="base-info">
          <a-tab-pane tab="基本信息" key="base-info" forceRender style="position: relative">
            <div style="padding: 20px">
              <CompanyDepartFormTab :data="companyDepartData" :rootTreeData="rootTreeData" @success="onSuccess" />
            </div>
          </a-tab-pane>
          <a-tab-pane tab="权限配置" key="role-info">
            <div style="padding: 0 20px 20px">
              <CompanyDepartRuleTab :data="companyDepartData" />
            </div>
          </a-tab-pane>
        </a-tabs>
        <div v-show="companyDepartData == null" style="height: 100%; display: flex; align-items: center; justify-content: center">
          <a-empty description="请从左侧选择单位或部门进行管理" />
        </div>
      </div>
    </a-col>
  </a-row>
</template>

<script lang="ts" setup name="company-depart-manage">
  import { provide, ref } from 'vue';
  import { useDesign } from '/@/hooks/web/useDesign';
  import CompanyDepartLeftTree from './components/CompanyDepartLeftTree.vue';
  import CompanyDepartFormTab from './components/CompanyDepartFormTab.vue';
  import CompanyDepartRuleTab from './components/CompanyDepartRuleTab.vue';

  const { prefixCls } = useDesign('company-depart-manage');
  provide('prefixCls', prefixCls);

  // 给子组件定义一个ref变量
  const leftTree = ref();

  // 当前选中的单位/部门信息
  const companyDepartData = ref({});
  const rootTreeData = ref<any[]>([]);

  // 左侧树选择后触发
  function onTreeSelect(data) {
    console.log('onTreeSelect: ', data);
    companyDepartData.value = data;
  }

  // 左侧树rootTreeData触发
  function onRootTreeData(data) {
    rootTreeData.value = data;
  }

  // 操作成功后刷新
  function onSuccess() {
    leftTree.value?.loadRootTreeData();
  }
</script>

<style lang="less" scoped>
  .company-depart-manage {
    &--box {
      height: calc(100vh - 120px);
      overflow: hidden;
    }
  }
</style>
