<template>
  <a-modal
    :title="title"
    width="90%"
    :open="visible"
    @ok="handleOk"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit || activeKey !== '1' } }"
    @cancel="handleCancel"
    cancelText="关闭"
    :destroy-on-close="true"
  >
    <a-tabs v-model:activeKey="activeKey" type="card" size="small" @tab-click="handleTabClick">
      <a-tab-pane key="1" tab="基本信息" style="padding: 0">
        <CompanyForm ref="registerForm" @ok="submitCallback" :formDisabled="disableSubmit" :formBpm="false" />
      </a-tab-pane>
      <a-tab-pane key="2" tab="部门管理" style="padding: 0" :forceRender="true" v-if="isUpdate">
        <CompanyDepartManagement :companyId="currentCompanyId" @success="handleDepartSuccess" />
      </a-tab-pane>
    </a-tabs>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, nextTick, defineExpose } from 'vue';
  import CompanyForm from './CompanyForm.vue';
  import CompanyDepartManagement from './CompanyDepartManagement.vue';

  const title = ref<string>('');
  const width = ref<number>(800);
  const visible = ref<boolean>(false);
  const disableSubmit = ref<boolean>(false);
  const registerForm = ref();
  const activeKey = ref('1');
  const isUpdate = ref<boolean>(false);
  const currentCompanyId = ref<string>('');
  const emit = defineEmits(['register', 'success']);

  /**
   * 新增
   */
  function add(obj = {}) {
    title.value = '新增单位';
    activeKey.value = '1';
    isUpdate.value = false;
    currentCompanyId.value = '';
    visible.value = true;
    nextTick(() => {
      registerForm.value.add(obj);
    });
  }

  /**
   * 编辑
   * @param record
   */
  function edit(record) {
    title.value = disableSubmit.value ? '单位详情' : '编辑单位';
    activeKey.value = '1';
    isUpdate.value = !!record.id;
    currentCompanyId.value = record.id || '';
    visible.value = true;
    nextTick(() => {
      registerForm.value.edit(record);
    });
  }

  /**
   * 确定按钮点击事件
   */
  function handleOk() {
    registerForm.value.submitForm();
  }

  /**
   * form保存回调事件
   */
  function submitCallback({ isUpdate, values, expandedArr, changeParent }) {
    handleCancel();
    emit('success', {
      isUpdate: isUpdate,
      values: values,
      expandedArr: expandedArr,
      // 是否更改了父级节点
      changeParent: changeParent,
    });
  }

  /**
   * 取消按钮回调事件
   */
  function handleCancel() {
    visible.value = false;
    activeKey.value = '1';
  }

  /**
   * 标签页点击事件
   */
  function handleTabClick(key) {
    activeKey.value = key;
  }

  /**
   * 部门管理成功回调
   */
  function handleDepartSuccess() {
    // 部门管理操作成功后的处理
    console.log('部门管理操作成功');
  }

  defineExpose({
    add,
    edit,
    disableSubmit,
  });
</script>

<style>
  /**隐藏样式-modal确定按钮 */
  .jee-hidden {
    display: none !important;
  }
</style>
