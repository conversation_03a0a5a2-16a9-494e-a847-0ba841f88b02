<template>
  <div class="modal-drawer-usage">
    <a-card title="弹窗和抽屉中使用Company自动完成组件">
      
      <!-- 触发按钮 -->
      <a-space size="large">
        <a-button type="primary" @click="showAddModal">新增客户（弹窗）</a-button>
        <a-button type="primary" @click="showEditDrawer">编辑项目（抽屉）</a-button>
        <a-button type="primary" @click="showQuickSelectModal">快速选择单位</a-button>
      </a-space>

      <!-- 新增客户弹窗 -->
      <a-modal
        v-model:open="addModalVisible"
        title="新增客户"
        width="800px"
        :confirmLoading="modalLoading"
        @ok="handleAddModalOk"
        @cancel="handleAddModalCancel"
      >
        <a-form :model="addForm" :rules="addFormRules" ref="addFormRef" layout="vertical">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="客户名称" name="customerName">
                <a-input v-model:value="addForm.customerName" placeholder="请输入客户名称" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="所属单位" name="companyId">
                <CompanyAutoComplete
                  v-model:value="addForm.companyId"
                  placeholder="请选择所属单位"
                  @select="handleAddFormCompanySelect"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="联系电话" name="phone">
                <a-input v-model:value="addForm.phone" placeholder="请输入联系电话" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="邮箱" name="email">
                <a-input v-model:value="addForm.email" placeholder="请输入邮箱" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-form-item label="备注" name="remark">
            <a-textarea v-model:value="addForm.remark" placeholder="请输入备注信息" :rows="3" />
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 编辑项目抽屉 -->
      <a-drawer
        v-model:open="editDrawerVisible"
        title="编辑项目信息"
        width="600px"
        :closable="true"
        @close="handleEditDrawerClose"
      >
        <a-form :model="editForm" :rules="editFormRules" ref="editFormRef" layout="vertical">
          <a-form-item label="项目名称" name="projectName">
            <a-input v-model:value="editForm.projectName" placeholder="请输入项目名称" />
          </a-form-item>
          
          <a-form-item label="委托单位" name="clientCompanyId">
            <CompanyAutoComplete
              v-model:value="editForm.clientCompanyId"
              placeholder="请选择委托单位"
              @select="handleEditFormClientCompanySelect"
            />
          </a-form-item>
          
          <a-form-item label="承接单位" name="contractorCompanyId">
            <CompanyAutoComplete
              v-model:value="editForm.contractorCompanyId"
              placeholder="请选择承接单位"
              @select="handleEditFormContractorCompanySelect"
            />
          </a-form-item>
          
          <a-form-item label="项目状态" name="status">
            <a-select v-model:value="editForm.status" placeholder="请选择项目状态">
              <a-select-option value="planning">规划中</a-select-option>
              <a-select-option value="executing">执行中</a-select-option>
              <a-select-option value="completed">已完成</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="项目描述" name="description">
            <a-textarea v-model:value="editForm.description" placeholder="请输入项目描述" :rows="4" />
          </a-form-item>
        </a-form>
        
        <template #footer>
          <a-space>
            <a-button @click="handleEditDrawerClose">取消</a-button>
            <a-button type="primary" @click="handleEditDrawerSave" :loading="drawerLoading">保存</a-button>
          </a-space>
        </template>
      </a-drawer>

      <!-- 快速选择单位弹窗 -->
      <a-modal
        v-model:open="quickSelectModalVisible"
        title="快速选择单位"
        width="500px"
        @ok="handleQuickSelectOk"
        @cancel="handleQuickSelectCancel"
      >
        <div class="quick-select-content">
          <a-form layout="vertical">
            <a-form-item label="选择单位">
              <CompanyAutoComplete
                v-model:value="quickSelectCompanyId"
                placeholder="请输入单位名称或助记码进行搜索"
                @select="handleQuickSelectCompanySelect"
              />
            </a-form-item>
          </a-form>
          
          <!-- 选中单位信息展示 -->
          <div v-if="quickSelectCompanyInfo" class="selected-company-info">
            <a-divider>选中单位信息</a-divider>
            <a-descriptions :column="1" size="small" bordered>
              <a-descriptions-item label="单位名称">{{ quickSelectCompanyInfo.name }}</a-descriptions-item>
              <a-descriptions-item label="单位简称" v-if="quickSelectCompanyInfo.shortName">
                {{ quickSelectCompanyInfo.shortName }}
              </a-descriptions-item>
              <a-descriptions-item label="助记码" v-if="quickSelectCompanyInfo.helpChar">
                {{ quickSelectCompanyInfo.helpChar }}
              </a-descriptions-item>
              <a-descriptions-item label="单位编码" v-if="quickSelectCompanyInfo.orgCode">
                {{ quickSelectCompanyInfo.orgCode }}
              </a-descriptions-item>
              <a-descriptions-item label="联系电话" v-if="quickSelectCompanyInfo.telephone">
                {{ quickSelectCompanyInfo.telephone }}
              </a-descriptions-item>
              <a-descriptions-item label="地址" v-if="quickSelectCompanyInfo.address">
                {{ quickSelectCompanyInfo.address }}
              </a-descriptions-item>
            </a-descriptions>
          </div>
        </div>
      </a-modal>

      <!-- 当前选择结果展示 -->
      <a-card title="操作结果" style="margin-top: 24px;" v-if="operationResults.length > 0">
        <a-timeline>
          <a-timeline-item v-for="(result, index) in operationResults" :key="index" :color="result.type === 'success' ? 'green' : 'blue'">
            <p><strong>{{ result.title }}</strong></p>
            <p>{{ result.description }}</p>
            <p class="result-time">{{ result.time }}</p>
          </a-timeline-item>
        </a-timeline>
      </a-card>

    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue';
  import { message } from 'ant-design-vue';
  import type { FormInstance } from 'ant-design-vue';
  import CompanyAutoComplete from '/@/components/basicinfo/CompanyAutoComplete.vue';
  import type { CompanyAutoCompleteDTO } from '/@/types/basicinfo/company';

  // 弹窗和抽屉状态
  const addModalVisible = ref(false);
  const editDrawerVisible = ref(false);
  const quickSelectModalVisible = ref(false);
  const modalLoading = ref(false);
  const drawerLoading = ref(false);

  // 表单引用
  const addFormRef = ref<FormInstance>();
  const editFormRef = ref<FormInstance>();

  // 新增表单数据
  const addForm = reactive({
    customerName: '',
    companyId: '',
    phone: '',
    email: '',
    remark: '',
  });

  const addFormRules = {
    customerName: [{ required: true, message: '请输入客户名称', trigger: 'blur' }],
    companyId: [{ required: true, message: '请选择所属单位', trigger: 'change' }],
    phone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
  };

  // 编辑表单数据
  const editForm = reactive({
    projectName: '示例项目',
    clientCompanyId: '',
    contractorCompanyId: '',
    status: 'planning',
    description: '',
  });

  const editFormRules = {
    projectName: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
    clientCompanyId: [{ required: true, message: '请选择委托单位', trigger: 'change' }],
    contractorCompanyId: [{ required: true, message: '请选择承接单位', trigger: 'change' }],
  };

  // 快速选择数据
  const quickSelectCompanyId = ref('');
  const quickSelectCompanyInfo = ref<CompanyAutoCompleteDTO | null>(null);

  // 操作结果记录
  const operationResults = ref<Array<{
    title: string;
    description: string;
    time: string;
    type: 'success' | 'info';
  }>>([]);

  // 添加操作结果
  const addOperationResult = (title: string, description: string, type: 'success' | 'info' = 'success') => {
    operationResults.value.unshift({
      title,
      description,
      time: new Date().toLocaleString(),
      type,
    });
    // 只保留最近10条记录
    if (operationResults.value.length > 10) {
      operationResults.value = operationResults.value.slice(0, 10);
    }
  };

  // 弹窗操作
  const showAddModal = () => {
    addModalVisible.value = true;
    // 重置表单
    Object.assign(addForm, {
      customerName: '',
      companyId: '',
      phone: '',
      email: '',
      remark: '',
    });
  };

  const handleAddModalOk = async () => {
    try {
      await addFormRef.value?.validate();
      modalLoading.value = true;
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      console.log('新增客户数据:', addForm);
      message.success('客户新增成功！');
      addOperationResult('新增客户', `成功新增客户：${addForm.customerName}`);
      
      addModalVisible.value = false;
    } catch (error) {
      console.error('表单验证失败:', error);
    } finally {
      modalLoading.value = false;
    }
  };

  const handleAddModalCancel = () => {
    addModalVisible.value = false;
  };

  // 抽屉操作
  const showEditDrawer = () => {
    editDrawerVisible.value = true;
  };

  const handleEditDrawerClose = () => {
    editDrawerVisible.value = false;
  };

  const handleEditDrawerSave = async () => {
    try {
      await editFormRef.value?.validate();
      drawerLoading.value = true;
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      console.log('编辑项目数据:', editForm);
      message.success('项目信息保存成功！');
      addOperationResult('编辑项目', `成功保存项目：${editForm.projectName}`);
      
      editDrawerVisible.value = false;
    } catch (error) {
      console.error('表单验证失败:', error);
    } finally {
      drawerLoading.value = false;
    }
  };

  // 快速选择操作
  const showQuickSelectModal = () => {
    quickSelectModalVisible.value = true;
    quickSelectCompanyId.value = '';
    quickSelectCompanyInfo.value = null;
  };

  const handleQuickSelectOk = () => {
    if (quickSelectCompanyInfo.value) {
      message.success(`已选择单位：${quickSelectCompanyInfo.value.name}`);
      addOperationResult('快速选择单位', `选择了单位：${quickSelectCompanyInfo.value.name}`);
    } else {
      message.warning('请先选择一个单位');
      return;
    }
    quickSelectModalVisible.value = false;
  };

  const handleQuickSelectCancel = () => {
    quickSelectModalVisible.value = false;
  };

  // 单位选择事件处理
  const handleAddFormCompanySelect = (value: string, option: CompanyAutoCompleteDTO) => {
    console.log('新增表单 - 选中单位:', option);
    // 自动填充电话号码
    if (option.telephone && !addForm.phone) {
      addForm.phone = option.telephone;
    }
    message.success(`已选择单位：${option.name}`);
  };

  const handleEditFormClientCompanySelect = (value: string, option: CompanyAutoCompleteDTO) => {
    console.log('编辑表单 - 选中委托单位:', option);
    message.success(`已选择委托单位：${option.name}`);
  };

  const handleEditFormContractorCompanySelect = (value: string, option: CompanyAutoCompleteDTO) => {
    console.log('编辑表单 - 选中承接单位:', option);
    message.success(`已选择承接单位：${option.name}`);
  };

  const handleQuickSelectCompanySelect = (value: string, option: CompanyAutoCompleteDTO) => {
    console.log('快速选择 - 选中单位:', option);
    quickSelectCompanyInfo.value = option;
    message.success(`已选择单位：${option.name}`);
  };
</script>

<style lang="less" scoped>
  .modal-drawer-usage {
    padding: 20px;
  }

  .quick-select-content {
    .selected-company-info {
      margin-top: 16px;
    }
  }

  .result-time {
    color: #999;
    font-size: 12px;
    margin-top: 4px;
  }

  // 弹窗内组件样式调整
  :deep(.ant-modal-body) {
    .ant-form-item {
      margin-bottom: 16px;
    }
  }

  // 抽屉内组件样式调整
  :deep(.ant-drawer-body) {
    .ant-form-item {
      margin-bottom: 16px;
    }
  }
</style>
