<template>
  <div class="business-usage-example">
    
    <!-- 客户管理模块示例 -->
    <a-card title="客户管理 - 新增客户" class="business-card">
      <a-form :model="customerForm" :rules="customerRules" ref="customerFormRef" layout="vertical">
        <a-row :gutter="24">
          <a-col :span="8">
            <a-form-item label="客户名称" name="customerName">
              <a-input v-model:value="customerForm.customerName" placeholder="请输入客户名称" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="所属单位" name="companyId">
              <CompanyAutoComplete
                v-model:value="customerForm.companyId"
                placeholder="请选择客户所属单位"
                @select="handleCustomerCompanySelect"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="客户类型" name="customerType">
              <a-select v-model:value="customerForm.customerType" placeholder="请选择客户类型">
                <a-select-option value="enterprise">企业客户</a-select-option>
                <a-select-option value="individual">个人客户</a-select-option>
                <a-select-option value="government">政府客户</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="24">
          <a-col :span="8">
            <a-form-item label="联系人" name="contactPerson">
              <a-input v-model:value="customerForm.contactPerson" placeholder="请输入联系人姓名" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="联系电话" name="contactPhone">
              <a-input v-model:value="customerForm.contactPhone" placeholder="请输入联系电话" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="邮箱" name="email">
              <a-input v-model:value="customerForm.email" placeholder="请输入邮箱地址" />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="24">
          <a-col :span="24">
            <a-form-item label="地址" name="address">
              <a-input v-model:value="customerForm.address" placeholder="请输入详细地址" />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item>
          <a-space>
            <a-button type="primary" @click="submitCustomer" :loading="submitting">保存客户</a-button>
            <a-button @click="resetCustomerForm">重置</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 项目管理模块示例 -->
    <a-card title="项目管理 - 项目信息" class="business-card">
      <a-form :model="projectForm" :rules="projectRules" ref="projectFormRef" layout="vertical">
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="项目名称" name="projectName">
              <a-input v-model:value="projectForm.projectName" placeholder="请输入项目名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="项目编号" name="projectCode">
              <a-input v-model:value="projectForm.projectCode" placeholder="请输入项目编号" />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="委托单位" name="clientCompanyId">
              <CompanyAutoComplete
                v-model:value="projectForm.clientCompanyId"
                placeholder="请选择委托单位"
                @select="handleClientCompanySelect"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="承接单位" name="contractorCompanyId">
              <CompanyAutoComplete
                v-model:value="projectForm.contractorCompanyId"
                placeholder="请选择承接单位"
                @select="handleContractorCompanySelect"
              />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="24">
          <a-col :span="8">
            <a-form-item label="项目状态" name="status">
              <a-select v-model:value="projectForm.status" placeholder="请选择项目状态">
                <a-select-option value="planning">规划中</a-select-option>
                <a-select-option value="executing">执行中</a-select-option>
                <a-select-option value="completed">已完成</a-select-option>
                <a-select-option value="suspended">已暂停</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="开始日期" name="startDate">
              <a-date-picker v-model:value="projectForm.startDate" placeholder="选择开始日期" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="结束日期" name="endDate">
              <a-date-picker v-model:value="projectForm.endDate" placeholder="选择结束日期" style="width: 100%" />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item>
          <a-space>
            <a-button type="primary" @click="submitProject" :loading="submitting">保存项目</a-button>
            <a-button @click="resetProjectForm">重置</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 合同管理模块示例 -->
    <a-card title="合同管理 - 合同信息" class="business-card">
      <a-form :model="contractForm" :rules="contractRules" ref="contractFormRef" layout="vertical">
        <a-row :gutter="24">
          <a-col :span="8">
            <a-form-item label="合同编号" name="contractNo">
              <a-input v-model:value="contractForm.contractNo" placeholder="请输入合同编号" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="合同名称" name="contractName">
              <a-input v-model:value="contractForm.contractName" placeholder="请输入合同名称" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="合同金额" name="contractAmount">
              <a-input-number 
                v-model:value="contractForm.contractAmount" 
                placeholder="请输入合同金额"
                :min="0"
                :precision="2"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="甲方单位" name="partyACompanyId">
              <CompanyAutoComplete
                v-model:value="contractForm.partyACompanyId"
                placeholder="请选择甲方单位"
                @select="handlePartyACompanySelect"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="乙方单位" name="partyBCompanyId">
              <CompanyAutoComplete
                v-model:value="contractForm.partyBCompanyId"
                placeholder="请选择乙方单位"
                @select="handlePartyBCompanySelect"
              />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="24">
          <a-col :span="8">
            <a-form-item label="签订日期" name="signDate">
              <a-date-picker v-model:value="contractForm.signDate" placeholder="选择签订日期" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="生效日期" name="effectiveDate">
              <a-date-picker v-model:value="contractForm.effectiveDate" placeholder="选择生效日期" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="到期日期" name="expiryDate">
              <a-date-picker v-model:value="contractForm.expiryDate" placeholder="选择到期日期" style="width: 100%" />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item>
          <a-space>
            <a-button type="primary" @click="submitContract" :loading="submitting">保存合同</a-button>
            <a-button @click="resetContractForm">重置</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue';
  import { message } from 'ant-design-vue';
  import type { FormInstance } from 'ant-design-vue';
  import CompanyAutoComplete from '/@/components/basicinfo/CompanyAutoComplete.vue';
  import type { CompanyAutoCompleteDTO } from '/@/types/basicinfo/company';

  // 表单引用
  const customerFormRef = ref<FormInstance>();
  const projectFormRef = ref<FormInstance>();
  const contractFormRef = ref<FormInstance>();
  
  // 提交状态
  const submitting = ref(false);

  // 客户表单数据
  const customerForm = reactive({
    customerName: '',
    companyId: '',
    customerType: '',
    contactPerson: '',
    contactPhone: '',
    email: '',
    address: '',
  });

  // 客户表单验证规则
  const customerRules = {
    customerName: [{ required: true, message: '请输入客户名称', trigger: 'blur' }],
    companyId: [{ required: true, message: '请选择所属单位', trigger: 'change' }],
    customerType: [{ required: true, message: '请选择客户类型', trigger: 'change' }],
    contactPerson: [{ required: true, message: '请输入联系人姓名', trigger: 'blur' }],
    contactPhone: [
      { required: true, message: '请输入联系电话', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
    ],
    email: [
      { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
    ],
  };

  // 项目表单数据
  const projectForm = reactive({
    projectName: '',
    projectCode: '',
    clientCompanyId: '',
    contractorCompanyId: '',
    status: '',
    startDate: null,
    endDate: null,
  });

  // 项目表单验证规则
  const projectRules = {
    projectName: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
    projectCode: [{ required: true, message: '请输入项目编号', trigger: 'blur' }],
    clientCompanyId: [{ required: true, message: '请选择委托单位', trigger: 'change' }],
    contractorCompanyId: [{ required: true, message: '请选择承接单位', trigger: 'change' }],
    status: [{ required: true, message: '请选择项目状态', trigger: 'change' }],
  };

  // 合同表单数据
  const contractForm = reactive({
    contractNo: '',
    contractName: '',
    contractAmount: null,
    partyACompanyId: '',
    partyBCompanyId: '',
    signDate: null,
    effectiveDate: null,
    expiryDate: null,
  });

  // 合同表单验证规则
  const contractRules = {
    contractNo: [{ required: true, message: '请输入合同编号', trigger: 'blur' }],
    contractName: [{ required: true, message: '请输入合同名称', trigger: 'blur' }],
    contractAmount: [{ required: true, message: '请输入合同金额', trigger: 'blur' }],
    partyACompanyId: [{ required: true, message: '请选择甲方单位', trigger: 'change' }],
    partyBCompanyId: [{ required: true, message: '请选择乙方单位', trigger: 'change' }],
    signDate: [{ required: true, message: '请选择签订日期', trigger: 'change' }],
  };

  // 事件处理函数
  const handleCustomerCompanySelect = (value: string, option: CompanyAutoCompleteDTO) => {
    console.log('客户管理 - 选中单位:', option);
    // 自动填充相关信息
    if (option.telephone && !customerForm.contactPhone) {
      customerForm.contactPhone = option.telephone;
    }
    if (option.address && !customerForm.address) {
      customerForm.address = option.address;
    }
    message.success(`已选择客户单位: ${option.name}`);
  };

  const handleClientCompanySelect = (value: string, option: CompanyAutoCompleteDTO) => {
    console.log('项目管理 - 选中委托单位:', option);
    message.success(`已选择委托单位: ${option.name}`);
  };

  const handleContractorCompanySelect = (value: string, option: CompanyAutoCompleteDTO) => {
    console.log('项目管理 - 选中承接单位:', option);
    message.success(`已选择承接单位: ${option.name}`);
  };

  const handlePartyACompanySelect = (value: string, option: CompanyAutoCompleteDTO) => {
    console.log('合同管理 - 选中甲方单位:', option);
    message.success(`已选择甲方单位: ${option.name}`);
  };

  const handlePartyBCompanySelect = (value: string, option: CompanyAutoCompleteDTO) => {
    console.log('合同管理 - 选中乙方单位:', option);
    message.success(`已选择乙方单位: ${option.name}`);
  };

  // 表单提交函数
  const submitCustomer = async () => {
    try {
      await customerFormRef.value?.validate();
      submitting.value = true;
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      console.log('提交客户数据:', customerForm);
      message.success('客户信息保存成功！');
      
    } catch (error) {
      console.error('客户表单验证失败:', error);
    } finally {
      submitting.value = false;
    }
  };

  const submitProject = async () => {
    try {
      await projectFormRef.value?.validate();
      submitting.value = true;
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      console.log('提交项目数据:', projectForm);
      message.success('项目信息保存成功！');
      
    } catch (error) {
      console.error('项目表单验证失败:', error);
    } finally {
      submitting.value = false;
    }
  };

  const submitContract = async () => {
    try {
      await contractFormRef.value?.validate();
      submitting.value = true;
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      console.log('提交合同数据:', contractForm);
      message.success('合同信息保存成功！');
      
    } catch (error) {
      console.error('合同表单验证失败:', error);
    } finally {
      submitting.value = false;
    }
  };

  // 重置表单函数
  const resetCustomerForm = () => {
    customerFormRef.value?.resetFields();
    message.info('客户表单已重置');
  };

  const resetProjectForm = () => {
    projectFormRef.value?.resetFields();
    message.info('项目表单已重置');
  };

  const resetContractForm = () => {
    contractFormRef.value?.resetFields();
    message.info('合同表单已重置');
  };
</script>

<style lang="less" scoped>
  .business-usage-example {
    padding: 20px;
    
    .business-card {
      margin-bottom: 24px;
      
      :deep(.ant-card-head-title) {
        color: #1890ff;
        font-weight: 600;
      }
    }
  }
</style>
