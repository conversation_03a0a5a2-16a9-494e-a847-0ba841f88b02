<template>
  <div class="company-autocomplete-usage">
    <a-card title="Company自动完成组件使用示例" class="usage-card">
      
      <!-- 示例1: 基础用法 -->
      <div class="example-section">
        <h3>1. 基础用法</h3>
        <a-form layout="vertical">
          <a-form-item label="选择单位">
            <CompanyAutoComplete
              v-model:value="form.companyId"
              @select="handleCompanySelect"
              @change="handleCompanyChange"
            />
          </a-form-item>
          <a-form-item label="选中的单位信息">
            <a-textarea 
              :value="JSON.stringify(selectedCompany, null, 2)" 
              :rows="6" 
              readonly 
              placeholder="选择单位后会显示详细信息"
            />
          </a-form-item>
        </a-form>
      </div>

      <a-divider />

      <!-- 示例2: 表单中使用 -->
      <div class="example-section">
        <h3>2. 在表单中使用</h3>
        <a-form :model="customerForm" layout="vertical">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="客户名称" name="customerName" :rules="[{ required: true, message: '请输入客户名称' }]">
                <a-input v-model:value="customerForm.customerName" placeholder="请输入客户名称" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="所属单位" name="companyId" :rules="[{ required: true, message: '请选择所属单位' }]">
                <CompanyAutoComplete
                  v-model:value="customerForm.companyId"
                  placeholder="请选择客户所属单位"
                  @select="handleCustomerCompanySelect"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="联系电话" name="phone">
                <a-input v-model:value="customerForm.phone" placeholder="请输入联系电话" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="邮箱" name="email">
                <a-input v-model:value="customerForm.email" placeholder="请输入邮箱" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-form-item>
            <a-space>
              <a-button type="primary" @click="submitCustomerForm">提交</a-button>
              <a-button @click="resetCustomerForm">重置</a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>

      <a-divider />

      <!-- 示例3: 自定义样式 -->
      <div class="example-section">
        <h3>3. 自定义样式和配置</h3>
        <a-form layout="vertical">
          <a-form-item label="禁用状态">
            <CompanyAutoComplete
              v-model:value="form.disabledCompanyId"
              :disabled="true"
              placeholder="这是禁用状态的组件"
            />
          </a-form-item>
          <a-form-item label="不允许清空">
            <CompanyAutoComplete
              v-model:value="form.noClearCompanyId"
              :allowClear="false"
              placeholder="不允许清空的组件"
            />
          </a-form-item>
          <a-form-item label="自定义占位符">
            <CompanyAutoComplete
              v-model:value="form.customPlaceholderCompanyId"
              placeholder="🏢 请输入或选择您的单位..."
              @select="handleCustomPlaceholderSelect"
            />
          </a-form-item>
        </a-form>
      </div>

      <a-divider />

      <!-- 示例4: 列表中使用 -->
      <div class="example-section">
        <h3>4. 在列表/表格中使用</h3>
        <a-table 
          :dataSource="tableData" 
          :columns="tableColumns" 
          :pagination="false"
          size="small"
        >
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.key === 'companyId'">
              <CompanyAutoComplete
                v-model:value="record.companyId"
                placeholder="选择单位"
                @select="(value, option) => handleTableCompanySelect(value, option, index)"
                style="width: 100%"
              />
            </template>
            <template v-if="column.key === 'action'">
              <a-space>
                <a-button size="small" @click="addTableRow">添加</a-button>
                <a-button size="small" danger @click="removeTableRow(index)">删除</a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>

      <a-divider />

      <!-- 示例5: 搜索过滤器 -->
      <div class="example-section">
        <h3>5. 作为搜索过滤器使用</h3>
        <a-form layout="inline" class="search-form">
          <a-form-item label="单位筛选">
            <CompanyAutoComplete
              v-model:value="searchForm.companyId"
              placeholder="选择单位进行筛选"
              :allowClear="true"
              @change="handleSearchCompanyChange"
              style="width: 300px"
            />
          </a-form-item>
          <a-form-item label="状态">
            <a-select v-model:value="searchForm.status" placeholder="选择状态" style="width: 120px">
              <a-select-option value="1">启用</a-select-option>
              <a-select-option value="0">禁用</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item>
            <a-space>
              <a-button type="primary" @click="handleSearch">搜索</a-button>
              <a-button @click="handleReset">重置</a-button>
            </a-space>
          </a-form-item>
        </a-form>
        
        <!-- 搜索结果展示 -->
        <div v-if="searchResults.length > 0" class="search-results">
          <h4>搜索结果：</h4>
          <a-list :dataSource="searchResults" size="small">
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #title>{{ item.name }}</template>
                  <template #description>
                    单位：{{ item.companyName }} | 状态：{{ item.status === '1' ? '启用' : '禁用' }}
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </div>
      </div>

    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue';
  import { message } from 'ant-design-vue';
  import CompanyAutoComplete from '/@/components/basicinfo/CompanyAutoComplete.vue';
  import type { CompanyAutoCompleteDTO } from '/@/types/basicinfo/company';

  // 基础用法数据
  const form = reactive({
    companyId: '',
    disabledCompanyId: '',
    noClearCompanyId: '',
    customPlaceholderCompanyId: '',
  });

  const selectedCompany = ref<CompanyAutoCompleteDTO | null>(null);

  // 表单数据
  const customerForm = reactive({
    customerName: '',
    companyId: '',
    phone: '',
    email: '',
  });

  // 表格数据
  const tableData = ref([
    { id: 1, name: '张三', companyId: '', department: '销售部' },
    { id: 2, name: '李四', companyId: '', department: '技术部' },
    { id: 3, name: '王五', companyId: '', department: '财务部' },
  ]);

  const tableColumns = [
    { title: '姓名', dataIndex: 'name', key: 'name' },
    { title: '所属单位', dataIndex: 'companyId', key: 'companyId', width: 300 },
    { title: '部门', dataIndex: 'department', key: 'department' },
    { title: '操作', key: 'action', width: 150 },
  ];

  // 搜索表单数据
  const searchForm = reactive({
    companyId: '',
    status: undefined,
  });

  const searchResults = ref([
    { id: 1, name: '项目A', companyName: '', status: '1' },
    { id: 2, name: '项目B', companyName: '', status: '0' },
  ]);

  // 事件处理函数
  const handleCompanySelect = (value: string, option: CompanyAutoCompleteDTO) => {
    console.log('基础用法 - 选中单位:', value, option);
    selectedCompany.value = option;
    message.success(`已选择单位: ${option.name}`);
  };

  const handleCompanyChange = (value: string, option?: CompanyAutoCompleteDTO) => {
    console.log('基础用法 - 单位变化:', value, option);
    if (!option) {
      selectedCompany.value = null;
      message.info('已清空单位选择');
    }
  };

  const handleCustomerCompanySelect = (value: string, option: CompanyAutoCompleteDTO) => {
    console.log('表单 - 选中客户单位:', value, option);
    // 可以自动填充相关信息
    if (option.telephone) {
      customerForm.phone = option.telephone;
    }
    message.success(`已选择客户单位: ${option.name}`);
  };

  const handleCustomPlaceholderSelect = (value: string, option: CompanyAutoCompleteDTO) => {
    console.log('自定义占位符 - 选中单位:', value, option);
    message.success(`选择了: ${option.name}`);
  };

  const handleTableCompanySelect = (value: string, option: CompanyAutoCompleteDTO, index: number) => {
    console.log(`表格第${index + 1}行 - 选中单位:`, value, option);
    message.success(`第${index + 1}行已选择单位: ${option.name}`);
  };

  const handleSearchCompanyChange = (value: string, option?: CompanyAutoCompleteDTO) => {
    console.log('搜索筛选 - 单位变化:', value, option);
    if (option) {
      // 更新搜索结果中的单位名称
      searchResults.value.forEach(item => {
        item.companyName = option.name;
      });
    } else {
      // 清空时重置单位名称
      searchResults.value.forEach(item => {
        item.companyName = '';
      });
    }
  };

  // 表单操作
  const submitCustomerForm = () => {
    console.log('提交客户表单:', customerForm);
    message.success('客户信息提交成功！');
  };

  const resetCustomerForm = () => {
    Object.assign(customerForm, {
      customerName: '',
      companyId: '',
      phone: '',
      email: '',
    });
    message.info('表单已重置');
  };

  // 表格操作
  const addTableRow = () => {
    const newId = Math.max(...tableData.value.map(item => item.id)) + 1;
    tableData.value.push({
      id: newId,
      name: `新员工${newId}`,
      companyId: '',
      department: '待分配',
    });
    message.success('已添加新行');
  };

  const removeTableRow = (index: number) => {
    tableData.value.splice(index, 1);
    message.success('已删除行');
  };

  // 搜索操作
  const handleSearch = () => {
    console.log('执行搜索:', searchForm);
    message.success('搜索完成');
  };

  const handleReset = () => {
    Object.assign(searchForm, {
      companyId: '',
      status: undefined,
    });
    searchResults.value.forEach(item => {
      item.companyName = '';
    });
    message.info('搜索条件已重置');
  };
</script>

<style lang="less" scoped>
  .company-autocomplete-usage {
    padding: 20px;
    
    .usage-card {
      max-width: 1200px;
      margin: 0 auto;
    }
    
    .example-section {
      margin-bottom: 30px;
      
      h3 {
        color: #1890ff;
        margin-bottom: 16px;
        font-weight: 600;
      }
    }
    
    .search-form {
      background: #fafafa;
      padding: 16px;
      border-radius: 6px;
      margin-bottom: 16px;
    }
    
    .search-results {
      background: #f9f9f9;
      padding: 16px;
      border-radius: 6px;
      
      h4 {
        margin-bottom: 12px;
        color: #333;
      }
    }
  }
</style>
