import { defHttp } from '/@/utils/http/axios';

enum Api {
  list = '/basicinfo/company/list',
  queryById = '/basicinfo/company/queryById',
  search = '/basicinfo/company/search',
  save = '/basicinfo/company/add',
  edit = '/basicinfo/company/edit',
  deleteOne = '/basicinfo/company/delete',
  deleteBatch = '/basicinfo/company/deleteBatch',
  importExcel = '/basicinfo/company/importExcel',
  exportXls = '/basicinfo/company/exportXls',
  autoComplete = '/basicinfo/company/autoComplete',
}

/**
 * 分页列表查询
 * @param params
 */
export const getCompanyList = (params) =>
  defHttp.get({ url: Api.list, params });

/**
 * 根据ID查询单位信息
 * @param params
 */
export const getCompanyById = (params) =>
  defHttp.get({ url: Api.queryById, params });

/**
 * 搜索单位（支持名称和助记码搜索）
 * @param params
 */
export const searchCompany = (params) =>
  defHttp.get({ url: Api.search, params });

/**
 * 新增单位
 * @param params
 */
export const saveCompany = (params) =>
  defHttp.post({ url: Api.save, params });

/**
 * 编辑单位
 * @param params
 */
export const editCompany = (params) =>
  defHttp.put({ url: Api.edit, params });

/**
 * 删除单位
 * @param params
 */
export const deleteCompany = (params) =>
  defHttp.delete({ url: Api.deleteOne, params });

/**
 * 批量删除单位
 * @param params
 */
export const batchDeleteCompany = (params) =>
  defHttp.delete({ url: Api.deleteBatch, params });

/**
 * 导出excel
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入excel
 */
export const getImportUrl = Api.importExcel;

/**
 * 获取没有父ID的单位列表（根级单位）
 * @param params
 */
export const getRootCompanyList = (params = {}) =>
  defHttp.get({ 
    url: Api.list, 
    params: {
      ...params,
      pid_NULL: true,
    }
  });

/**
 * 根据名称或助记码搜索单位
 * @param keyword 搜索关键词
 * @param onlyRoot 是否只搜索根级单位
 */
export const searchCompanyByKeyword = (keyword: string, onlyRoot: boolean = true) => {
  const params: any = {
    pageNo: 1,
    pageSize: 50,
  };

  if (onlyRoot) {
    params.pid_NULL = true;
  }

  if (keyword && keyword.trim()) {
    params.name_LIKE = keyword.trim();
    params.helpChar_LIKE = keyword.trim();
    params._searchMode = 'OR';
  }

  return defHttp.get({ url: Api.list, params });
};

/**
 * 单位自动完成接口（带缓存）
 * @param keyword 搜索关键词
 * @param pageSize 返回数量限制，默认50
 */
export const getCompanyAutoComplete = (keyword?: string, pageSize: number = 50) => {
  const params: any = {
    pageSize,
  };

  if (keyword && keyword.trim()) {
    params.keyword = keyword.trim();
  }

  return defHttp.get({ url: Api.autoComplete, params });
};
