# Company Auto Complete 组件

单位（Company）自动完成组件，支持通过名称、简称、助记码、编码进行搜索，只检索没有父ID的记录（根级单位），选中下拉项后向外抛出所选项目的完整数据。

## 功能特性

- ✅ 支持名称、简称、助记码、编码多字段搜索
- ✅ 只检索父级单位（没有父ID的记录）
- ✅ 后端缓存机制，提升查询性能
- ✅ 防抖搜索，避免频繁请求
- ✅ 支持清空操作
- ✅ 完整的TypeScript类型支持
- ✅ 向外抛出完整的单位数据对象
- ✅ 优化的显示界面，展示更多有用信息

## 组件属性 (Props)

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `value` | `string` | `''` | 当前选中的值（单位ID） |
| `placeholder` | `string` | `'请输入单位名称或助记码'` | 输入框占位符 |
| `allowClear` | `boolean` | `true` | 是否允许清空 |
| `disabled` | `boolean` | `false` | 是否禁用 |

## 组件事件 (Events)

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:value` | `(value: string)` | 值更新事件，用于v-model |
| `change` | `(value: string, option?: CompanyAutoCompleteDTO)` | 值变化事件 |
| `select` | `(value: string, option: CompanyAutoCompleteDTO)` | 选择事件，返回完整的单位对象 |

## 基础用法

```vue
<template>
  <CompanyAutoComplete
    v-model:value="selectedCompanyId"
    placeholder="请输入单位名称或助记码"
    @select="handleCompanySelect"
    @change="handleCompanyChange"
  />
</template>

<script setup>
import { ref } from 'vue';
import CompanyAutoComplete from '/@/components/basicinfo/CompanyAutoComplete.vue';

const selectedCompanyId = ref('');

const handleCompanySelect = (value, option) => {
  console.log('选中的单位:', option);
  // option 包含完整的单位信息：
  // {
  //   id: '单位ID',
  //   name: '单位名称',
  //   shortName: '单位简称',
  //   helpChar: '助记码',
  //   orgCode: '单位编码',
  //   telephone: '联系电话',
  //   address: '地址'
  // }
};

const handleCompanyChange = (value, option) => {
  console.log('值变化:', value, option);
};
</script>
```

## 自定义占位符用法

```vue
<template>
  <CompanyAutoComplete
    v-model:value="selectedCompanyId"
    placeholder="请选择您的单位..."
    @select="handleCompanySelect"
  />
</template>
```

## 表单集成

```vue
<template>
  <a-form :model="formData" :rules="rules">
    <a-form-item label="单位" name="companyId">
      <CompanyAutoComplete
        v-model:value="formData.companyId"
        @select="handleCompanySelect"
      />
    </a-form-item>
  </a-form>
</template>

<script setup>
import { reactive } from 'vue';

const formData = reactive({
  companyId: '',
  companyName: '',
});

const rules = {
  companyId: [
    { required: true, message: '请选择单位', trigger: 'change' }
  ],
};

const handleCompanySelect = (value, option) => {
  formData.companyId = value;
  formData.companyName = option.name;
};
</script>
```

## 数据结构

### CompanyAutoCompleteDTO 接口

```typescript
interface CompanyAutoCompleteDTO {
  /** 单位ID */
  id: string;
  /** 单位名称 */
  name: string;
  /** 单位简称 */
  shortName?: string;
  /** 助记码 */
  helpChar?: string;
  /** 单位编码 */
  orgCode?: string;
  /** 联系电话 */
  telephone?: string;
  /** 地址 */
  address?: string;
}
```

## API 接口

组件依赖以下后端接口：

- `GET /basicinfo/company/autoComplete` - 单位自动完成接口（带缓存）

### 请求参数

| 参数名 | 类型 | 说明 |
|--------|------|------|
| `keyword` | `string` | 搜索关键词（可选），支持名称、简称、助记码、编码搜索 |
| `pageSize` | `number` | 返回数量限制，默认50，最多100 |

### 缓存机制

- 后端使用Spring Boot缓存注解实现Redis缓存
- 缓存键格式：`company_autocomplete_cache:{keyword}_{pageSize}`
- 缓存过期时间：6小时
- 当单位数据发生变更时自动清除缓存

### 响应格式

```json
{
  "success": true,
  "result": [
    {
      "id": "1",
      "name": "测试单位",
      "shortName": "测试",
      "helpChar": "CSDW",
      "orgCode": "TEST001",
      "telephone": "13800138000",
      "address": "测试地址"
    }
  ]
}
```

## 样式定制

组件提供了基础的样式，可以通过CSS变量或者覆盖样式类来自定义外观：

```less
// 自定义选项样式
:deep(.company-option) {
  .company-name {
    font-weight: bold;
    color: #1890ff;
  }

  .company-details {
    .company-help-char {
      background: #e6f7ff;
      color: #1890ff;
    }

    .company-code,
    .company-phone {
      color: #666;
    }
  }
}
```

## 注意事项

1. 组件只检索没有父ID的记录（父级单位），确保数据的层级结构清晰
2. 搜索功能使用防抖处理，延迟300ms执行，避免频繁请求
3. 选择项目后，`select` 事件会返回完整的单位对象，方便获取所有相关信息
4. 组件支持清空操作，清空后会重新加载父级单位列表
5. 后端使用缓存机制，提升查询性能，缓存会在数据变更时自动清除
6. 确保后端接口 `/basicinfo/company/autoComplete` 已正确实现
7. 确保Redis缓存服务正常运行，缓存配置已正确加载
