# Company自动完成组件使用例子

## 📖 目录

1. [基础使用](#基础使用)
2. [表单中使用](#表单中使用)
3. [表格中使用](#表格中使用)
4. [弹窗和抽屉中使用](#弹窗和抽屉中使用)
5. [搜索过滤器](#搜索过滤器)
6. [自定义配置](#自定义配置)
7. [事件处理](#事件处理)
8. [最佳实践](#最佳实践)

## 🚀 基础使用

### 最简单的使用方式

```vue
<template>
  <CompanyAutoComplete
    v-model:value="companyId"
    @select="handleCompanySelect"
  />
</template>

<script setup>
import { ref } from 'vue';
import CompanyAutoComplete from '/@/components/basicinfo/CompanyAutoComplete.vue';

const companyId = ref('');

const handleCompanySelect = (value, option) => {
  console.log('选中的单位:', option);
};
</script>
```

### 带占位符和清空功能

```vue
<template>
  <CompanyAutoComplete
    v-model:value="companyId"
    placeholder="请选择您的单位..."
    :allowClear="true"
    @select="handleCompanySelect"
    @change="handleCompanyChange"
  />
</template>

<script setup>
const handleCompanyChange = (value, option) => {
  if (!option) {
    console.log('单位已清空');
  }
};
</script>
```

## 📝 表单中使用

### 在Ant Design Vue表单中使用

```vue
<template>
  <a-form :model="form" :rules="rules" ref="formRef">
    <a-form-item label="客户名称" name="customerName">
      <a-input v-model:value="form.customerName" />
    </a-form-item>
    
    <a-form-item label="所属单位" name="companyId">
      <CompanyAutoComplete
        v-model:value="form.companyId"
        placeholder="请选择所属单位"
        @select="handleCompanySelect"
      />
    </a-form-item>
    
    <a-form-item>
      <a-button type="primary" @click="submitForm">提交</a-button>
    </a-form-item>
  </a-form>
</template>

<script setup>
import { reactive, ref } from 'vue';

const formRef = ref();
const form = reactive({
  customerName: '',
  companyId: '',
});

const rules = {
  customerName: [{ required: true, message: '请输入客户名称' }],
  companyId: [{ required: true, message: '请选择所属单位' }],
};

const handleCompanySelect = (value, option) => {
  // 可以根据选中的单位自动填充其他字段
  console.log('选中单位:', option);
};

const submitForm = async () => {
  try {
    await formRef.value.validate();
    console.log('表单数据:', form);
  } catch (error) {
    console.error('验证失败:', error);
  }
};
</script>
```

### 自动填充相关字段

```vue
<template>
  <a-form :model="form" layout="vertical">
    <a-row :gutter="16">
      <a-col :span="12">
        <a-form-item label="所属单位" name="companyId">
          <CompanyAutoComplete
            v-model:value="form.companyId"
            @select="handleCompanySelect"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="联系电话" name="phone">
          <a-input v-model:value="form.phone" />
        </a-form-item>
      </a-col>
    </a-row>
    <a-form-item label="地址" name="address">
      <a-input v-model:value="form.address" />
    </a-form-item>
  </a-form>
</template>

<script setup>
const form = reactive({
  companyId: '',
  phone: '',
  address: '',
});

const handleCompanySelect = (value, option) => {
  // 自动填充电话和地址
  if (option.telephone && !form.phone) {
    form.phone = option.telephone;
  }
  if (option.address && !form.address) {
    form.address = option.address;
  }
};
</script>
```

## 📊 表格中使用

### 可编辑表格

```vue
<template>
  <a-table :dataSource="tableData" :columns="columns">
    <template #bodyCell="{ column, record, index }">
      <template v-if="column.key === 'companyId'">
        <CompanyAutoComplete
          v-model:value="record.companyId"
          placeholder="选择单位"
          @select="(value, option) => handleTableCompanySelect(value, option, index)"
        />
      </template>
    </template>
  </a-table>
</template>

<script setup>
const tableData = ref([
  { id: 1, name: '张三', companyId: '', department: '销售部' },
  { id: 2, name: '李四', companyId: '', department: '技术部' },
]);

const columns = [
  { title: '姓名', dataIndex: 'name', key: 'name' },
  { title: '所属单位', dataIndex: 'companyId', key: 'companyId' },
  { title: '部门', dataIndex: 'department', key: 'department' },
];

const handleTableCompanySelect = (value, option, index) => {
  console.log(`第${index + 1}行选中单位:`, option);
  // 可以根据选中的单位更新其他字段
  tableData.value[index].companyName = option.name;
};
</script>
```

## 🔍 搜索过滤器

### 作为搜索条件

```vue
<template>
  <div class="search-container">
    <a-form layout="inline">
      <a-form-item label="单位筛选">
        <CompanyAutoComplete
          v-model:value="searchForm.companyId"
          placeholder="选择单位进行筛选"
          :allowClear="true"
          @change="handleSearchCompanyChange"
          style="width: 300px"
        />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="handleSearch">搜索</a-button>
        <a-button @click="handleReset">重置</a-button>
      </a-form-item>
    </a-form>
    
    <!-- 搜索结果 -->
    <a-table :dataSource="searchResults" :columns="resultColumns" />
  </div>
</template>

<script setup>
const searchForm = reactive({
  companyId: '',
  companyName: '',
});

const searchResults = ref([]);

const handleSearchCompanyChange = (value, option) => {
  if (option) {
    searchForm.companyName = option.name;
  } else {
    searchForm.companyName = '';
  }
  // 实时搜索
  handleSearch();
};

const handleSearch = async () => {
  // 调用搜索API
  const params = {
    companyId: searchForm.companyId,
    // 其他搜索条件...
  };
  
  try {
    // const response = await searchAPI(params);
    // searchResults.value = response.data;
    console.log('搜索参数:', params);
  } catch (error) {
    console.error('搜索失败:', error);
  }
};
</script>
```

## 🎨 自定义配置

### 禁用状态

```vue
<template>
  <CompanyAutoComplete
    v-model:value="companyId"
    :disabled="isDisabled"
    placeholder="禁用状态"
  />
</template>

<script setup>
const isDisabled = ref(true);
</script>
```

### 不允许清空

```vue
<template>
  <CompanyAutoComplete
    v-model:value="companyId"
    :allowClear="false"
    placeholder="不允许清空"
  />
</template>
```

### 自定义样式

```vue
<template>
  <CompanyAutoComplete
    v-model:value="companyId"
    class="custom-company-select"
  />
</template>

<style scoped>
.custom-company-select {
  width: 100%;
}

.custom-company-select :deep(.ant-select-selector) {
  border-color: #1890ff;
}

.custom-company-select :deep(.company-option) {
  .company-name {
    color: #1890ff;
    font-weight: bold;
  }
}
</style>
```

## 🎯 事件处理

### 完整的事件处理示例

```vue
<template>
  <CompanyAutoComplete
    v-model:value="companyId"
    @select="handleSelect"
    @change="handleChange"
    @search="handleSearch"
    @clear="handleClear"
  />
</template>

<script setup>
const handleSelect = (value, option) => {
  console.log('选择事件:', { value, option });
  // 处理选择逻辑
};

const handleChange = (value, option) => {
  console.log('变化事件:', { value, option });
  // 处理变化逻辑
};

const handleSearch = (searchText) => {
  console.log('搜索事件:', searchText);
  // 处理搜索逻辑
};

const handleClear = () => {
  console.log('清空事件');
  // 处理清空逻辑
};
</script>
```

## 💡 最佳实践

### 1. 表单验证集成

```vue
<template>
  <a-form-item 
    label="所属单位" 
    name="companyId"
    :rules="companyRules"
  >
    <CompanyAutoComplete
      v-model:value="form.companyId"
      @select="handleCompanySelect"
    />
  </a-form-item>
</template>

<script setup>
const companyRules = [
  { required: true, message: '请选择所属单位', trigger: 'change' }
];
</script>
```

### 2. 数据联动

```vue
<script setup>
const handleCompanySelect = (value, option) => {
  // 根据选中的单位联动其他字段
  form.companyName = option.name;
  form.companyCode = option.orgCode;
  
  // 清空依赖字段
  form.departmentId = '';
  form.departmentName = '';
  
  // 重新加载部门数据
  loadDepartments(value);
};
</script>
```

### 3. 错误处理

```vue
<script setup>
const handleCompanySelect = (value, option) => {
  try {
    // 业务逻辑处理
    processCompanySelection(option);
  } catch (error) {
    console.error('处理单位选择时出错:', error);
    message.error('处理失败，请重试');
  }
};
</script>
```

### 4. 性能优化

```vue
<script setup>
import { debounce } from 'lodash-es';

// 防抖处理搜索
const debouncedSearch = debounce((searchText) => {
  // 执行搜索逻辑
}, 300);

const handleSearch = (searchText) => {
  debouncedSearch(searchText);
};
</script>
```

## 🔧 常见问题解决

### 1. 组件不显示数据
- 检查后端API是否正常
- 确认网络请求是否成功
- 检查数据格式是否正确

### 2. 选择后不触发事件
- 确认事件名称是否正确
- 检查事件处理函数是否定义
- 查看控制台是否有错误信息

### 3. 样式显示异常
- 检查CSS样式是否冲突
- 确认组件版本是否兼容
- 查看浏览器开发者工具

### 4. 表单验证不生效
- 确认表单项name属性设置正确
- 检查验证规则配置
- 确认v-model绑定正确

## 📚 相关文档

- [组件API文档](./README.md)
- [测试指南](./TESTING.md)
- [类型定义](../../types/basicinfo/company.ts)
