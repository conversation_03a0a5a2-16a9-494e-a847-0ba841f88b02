# Company自动完成组件测试指南

## 测试环境准备

### 1. 后端服务启动
确保后端服务已启动并且以下配置正确：

- Redis服务正常运行
- 缓存配置已加载（`application-company-cache.yml`）
- Company数据表中有测试数据

### 2. 前端环境
确保前端开发服务器已启动：
```bash
npm run dev
# 或
yarn dev
```

## 功能测试

### 1. 基础功能测试

#### 1.1 组件加载测试
- [ ] 访问包含CompanyAutoComplete组件的页面
- [ ] 确认组件正常渲染
- [ ] 确认占位符文本显示正确

#### 1.2 搜索功能测试
- [ ] 点击输入框，确认显示默认的父级单位列表
- [ ] 输入单位名称，确认搜索结果正确
- [ ] 输入助记码，确认搜索结果正确
- [ ] 输入单位编码，确认搜索结果正确
- [ ] 输入不存在的关键词，确认显示"暂无数据"

#### 1.3 选择功能测试
- [ ] 点击选择某个单位，确认输入框显示单位名称
- [ ] 确认`select`事件被正确触发
- [ ] 确认`change`事件被正确触发
- [ ] 确认返回的数据结构正确

#### 1.4 清空功能测试
- [ ] 选择单位后点击清空按钮
- [ ] 确认输入框被清空
- [ ] 确认重新显示默认单位列表

### 2. 缓存机制测试

#### 2.1 缓存生效测试
1. 打开浏览器开发者工具的Network面板
2. 首次搜索某个关键词（如"测试"）
3. 记录请求时间
4. 再次搜索相同关键词
5. 确认第二次请求响应更快（缓存命中）

#### 2.2 缓存键测试
使用Redis客户端工具检查缓存：
```bash
# 连接Redis
redis-cli

# 查看缓存键
KEYS company_autocomplete_cache:*

# 查看具体缓存内容
GET "company_autocomplete_cache:测试_50"
```

#### 2.3 缓存清除测试
1. 搜索并确认缓存生成
2. 在后台修改Company数据（添加、编辑、删除）
3. 再次搜索相同关键词
4. 确认返回最新数据（缓存已清除）

### 3. 性能测试

#### 3.1 响应时间测试
- [ ] 首次搜索响应时间 < 1秒
- [ ] 缓存命中响应时间 < 200ms
- [ ] 防抖功能正常（300ms延迟）

#### 3.2 并发测试
- [ ] 多个用户同时搜索不同关键词
- [ ] 确认缓存机制正常工作
- [ ] 确认没有缓存冲突

### 4. 边界条件测试

#### 4.1 数据边界测试
- [ ] 搜索空字符串
- [ ] 搜索特殊字符
- [ ] 搜索超长字符串
- [ ] 数据库无数据时的表现

#### 4.2 网络异常测试
- [ ] 断网情况下的错误处理
- [ ] 服务器错误时的错误处理
- [ ] 超时情况的处理

## 测试用例

### 测试用例1：基础搜索功能
```
前置条件：数据库中存在名为"测试单位"的公司，助记码为"CSDW"
测试步骤：
1. 在输入框中输入"测试"
2. 等待搜索结果
3. 点击选择"测试单位"
预期结果：
- 搜索结果中包含"测试单位"
- 选择后输入框显示"测试单位"
- 触发select事件，返回完整的单位信息
```

### 测试用例2：缓存机制验证
```
前置条件：Redis服务正常运行
测试步骤：
1. 首次搜索"测试"关键词
2. 记录响应时间T1
3. 立即再次搜索"测试"关键词
4. 记录响应时间T2
预期结果：
- T2 < T1（缓存命中，响应更快）
- Redis中存在对应的缓存键
```

### 测试用例3：缓存更新机制
```
前置条件：已有缓存数据
测试步骤：
1. 搜索"测试"，确认结果
2. 在后台添加新的包含"测试"的单位
3. 再次搜索"测试"
预期结果：
- 搜索结果包含新添加的单位
- 缓存已自动更新
```

## 常见问题排查

### 1. 组件不显示数据
- 检查后端API接口是否正常
- 检查网络请求是否成功
- 检查数据库中是否有父级单位数据

### 2. 缓存不生效
- 检查Redis服务是否运行
- 检查缓存配置是否正确加载
- 检查Spring Boot缓存注解是否生效

### 3. 搜索结果不准确
- 检查后端搜索逻辑
- 检查数据库数据是否正确
- 检查查询条件是否正确

### 4. 性能问题
- 检查数据库索引
- 检查缓存配置
- 检查网络延迟

## 测试报告模板

```
测试日期：____
测试人员：____
测试环境：____

功能测试结果：
□ 基础功能 - 通过/失败
□ 搜索功能 - 通过/失败  
□ 选择功能 - 通过/失败
□ 清空功能 - 通过/失败

缓存测试结果：
□ 缓存生效 - 通过/失败
□ 缓存清除 - 通过/失败

性能测试结果：
□ 响应时间 - 通过/失败
□ 并发测试 - 通过/失败

问题记录：
1. ____
2. ____

建议：
1. ____
2. ____
```
