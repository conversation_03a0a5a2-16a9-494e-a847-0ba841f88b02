<template>
  <div class="company-autocomplete-example">
    <h3>单位自动完成组件使用示例</h3>
    
    <a-space direction="vertical" size="large" style="width: 100%">
      <!-- 基础用法 -->
      <a-card title="基础用法" size="small">
        <a-form layout="vertical">
          <a-form-item label="选择单位">
            <CompanyAutoComplete
              v-model:value="selectedCompanyId1"
              placeholder="请输入单位名称或助记码"
              @select="handleCompanySelect1"
              @change="handleCompanyChange1"
            />
          </a-form-item>
          <a-form-item label="选中的单位信息">
            <a-textarea 
              :value="JSON.stringify(selectedCompany1, null, 2)" 
              :rows="4" 
              readonly 
            />
          </a-form-item>
        </a-form>
      </a-card>

      <!-- 自定义占位符 -->
      <a-card title="自定义占位符" size="small">
        <a-form layout="vertical">
          <a-form-item label="选择单位（自定义占位符）">
            <CompanyAutoComplete
              v-model:value="selectedCompanyId2"
              placeholder="请选择您的单位..."
              @select="handleCompanySelect2"
              @change="handleCompanyChange2"
            />
          </a-form-item>
          <a-form-item label="选中的单位信息">
            <a-textarea
              :value="JSON.stringify(selectedCompany2, null, 2)"
              :rows="4"
              readonly
            />
          </a-form-item>
        </a-form>
      </a-card>

      <!-- 禁用状态 -->
      <a-card title="禁用状态" size="small">
        <a-form layout="vertical">
          <a-form-item label="禁用的单位选择">
            <CompanyAutoComplete
              v-model:value="selectedCompanyId3"
              :disabled="true"
              placeholder="此组件已禁用"
            />
          </a-form-item>
          <a-form-item>
            <a-button @click="toggleDisabled">
              {{ disabled ? '启用' : '禁用' }} 组件
            </a-button>
          </a-form-item>
        </a-form>
      </a-card>

      <!-- 表单集成示例 -->
      <a-card title="表单集成示例" size="small">
        <a-form
          :model="formData"
          :rules="formRules"
          layout="vertical"
          @finish="handleSubmit"
        >
          <a-form-item label="单位名称" name="companyId">
            <CompanyAutoComplete
              v-model:value="formData.companyId"
              placeholder="请选择单位"
              @select="handleFormCompanySelect"
            />
          </a-form-item>
          <a-form-item label="业务描述" name="description">
            <a-textarea 
              v-model:value="formData.description" 
              placeholder="请输入业务描述"
              :rows="3"
            />
          </a-form-item>
          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit">提交</a-button>
              <a-button @click="resetForm">重置</a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </a-card>
    </a-space>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue';
  import { message } from 'ant-design-vue';
  import CompanyAutoComplete from './CompanyAutoComplete.vue';
  import type { CompanyAutoCompleteDTO } from '/@/types/basicinfo/company';

  // 基础用法
  const selectedCompanyId1 = ref<string>('');
  const selectedCompany1 = ref<CompanyAutoCompleteDTO | null>(null);

  const handleCompanySelect1 = (value: string, option: CompanyAutoCompleteDTO) => {
    console.log('选中单位1:', value, option);
    selectedCompany1.value = option;
    message.success(`已选择单位: ${option.name}`);
  };

  const handleCompanyChange1 = (value: string, option?: CompanyAutoCompleteDTO) => {
    console.log('单位变化1:', value, option);
    if (!option) {
      selectedCompany1.value = null;
    }
  };

  // 自定义占位符用法
  const selectedCompanyId2 = ref<string>('');
  const selectedCompany2 = ref<CompanyAutoCompleteDTO | null>(null);

  const handleCompanySelect2 = (value: string, option: CompanyAutoCompleteDTO) => {
    console.log('选中单位2:', value, option);
    selectedCompany2.value = option;
    message.success(`已选择单位: ${option.name}`);
  };

  const handleCompanyChange2 = (value: string, option?: CompanyAutoCompleteDTO) => {
    console.log('单位变化2:', value, option);
    if (!option) {
      selectedCompany2.value = null;
    }
  };

  // 禁用状态
  const selectedCompanyId3 = ref<string>('');
  const disabled = ref<boolean>(true);

  const toggleDisabled = () => {
    disabled.value = !disabled.value;
  };

  // 表单集成示例
  const formData = reactive({
    companyId: '',
    companyName: '',
    description: '',
  });

  const formRules = {
    companyId: [
      { required: true, message: '请选择单位', trigger: 'change' }
    ],
    description: [
      { required: true, message: '请输入业务描述', trigger: 'blur' }
    ],
  };

  const handleFormCompanySelect = (value: string, option: CompanyAutoCompleteDTO) => {
    formData.companyId = value;
    formData.companyName = option.name;
    console.log('表单单位选择:', value, option);
  };

  const handleSubmit = (values: any) => {
    console.log('表单提交:', values);
    message.success('表单提交成功！');
  };

  const resetForm = () => {
    formData.companyId = '';
    formData.companyName = '';
    formData.description = '';
    message.info('表单已重置');
  };
</script>

<style lang="less" scoped>
  .company-autocomplete-example {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;

    h3 {
      margin-bottom: 20px;
      text-align: center;
    }

    .ant-card {
      margin-bottom: 16px;
    }
  }
</style>
